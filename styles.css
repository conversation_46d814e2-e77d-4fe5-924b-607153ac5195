/* Reset y variables */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #4f46e5;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --purple-color: #8b5cf6;
  --info-color: #06b6d4;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --border-radius: 12px;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --transition: all 0.2s ease;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  min-height: 100vh;
  color: var(--gray-800);
  line-height: 1.6;
}

/* Navegación */
.navbar {
  background: white;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--gray-900);
}

.nav-brand i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.nav-menu {
  display: flex;
  gap: 1rem;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: var(--gray-600);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
}

.nav-btn:hover,
.nav-btn.active {
  background: var(--gray-100);
  color: var(--primary-color);
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--gray-600);
  cursor: pointer;
}

/* Indicador de conexión */
.connection-status {
  position: fixed;
  top: 70px;
  right: 1rem;
  background: white;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  z-index: 999;
}

.connection-status.connected i {
  color: var(--success-color);
}

.connection-status.disconnected i {
  color: var(--danger-color);
}

.connection-status.checking i {
  color: var(--warning-color);
}

/* Layout principal */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.view {
  display: none;
  min-height: calc(100vh - 64px);
}

.view.active {
  display: block;
}

/* Pantalla de bienvenida */
.welcome-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-top: 2rem;
}

.header-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.header-icon i {
  font-size: 2.5rem;
  color: white;
}

.main-title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin-bottom: 2rem;
}

.badges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--gray-100);
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--gray-700);
}

.badge i {
  color: var(--primary-color);
}

/* Características */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  text-align: center;
  transition: var(--transition);
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.feature-icon.blue {
  background: var(--primary-color);
}

.feature-icon.green {
  background: var(--success-color);
}

.feature-icon.purple {
  background: var(--purple-color);
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.feature-card p {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Tarjeta de información */
.info-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 3rem;
  border: 2px solid var(--success-color);
}

.info-header {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  padding: 1.5rem;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.info-header i {
  color: var(--success-color);
  font-size: 1.25rem;
}

.info-header h3 {
  color: var(--gray-800);
  font-size: 1.25rem;
}

.info-content {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.info-section h4 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.check-list {
  list-style: none;
}

.check-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--gray-600);
}

.check-list i {
  color: var(--success-color);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--gray-700);
}

/* Visualizaciones de Clustering Mejoradas */
.visualizations-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
  border: 2px solid var(--primary-color);
  overflow: hidden;
}

.visualizations-header {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.visualizations-header i {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.visualizations-header h3 {
  color: var(--gray-800);
  font-size: 1.25rem;
  margin: 0;
}

.visualizations-content {
  padding: 2rem;
  transition: opacity 0.3s ease;
}

/* Selector de K Mejorado */
.k-selector-section {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 2px solid var(--gray-200);
}

.k-selector-section h4 {
  margin-bottom: 1.5rem;
  color: var(--gray-800);
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.k-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.k-selector label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 1rem;
}

.k-select-enhanced {
  padding: 0.75rem 1rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius);
  background: white;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  min-width: 300px;
  font-weight: 500;
}

.k-select-enhanced:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.k-info-enhanced {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.k-optimal,
.k-current {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 600;
}

.k-optimal {
  background: var(--success-color);
  color: white;
}

.k-current {
  background: var(--info-color);
  color: white;
}

.k-explanation {
  background: white;
  padding: 1rem;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  margin-top: 1rem;
}

.k-explanation p {
  margin: 0;
  color: var(--gray-700);
  line-height: 1.6;
}

.highlight-green {
  background: #dcfce7;
  color: #15803d;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-weight: 600;
}

.highlight-orange {
  background: #fed7aa;
  color: #c2410c;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-weight: 600;
}

.highlight-red {
  background: #fecaca;
  color: #dc2626;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-weight: 600;
}

/* Items de visualización mejorados */
.visualization-item {
  margin-bottom: 2rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.visualization-item.enhanced {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.visualization-item h4 {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  padding: 1rem 1.5rem;
  margin: 0;
  color: var(--gray-800);
  border-bottom: 1px solid var(--gray-200);
  font-size: 1.125rem;
  font-weight: 600;
}

.visualization-item .plot-container {
  padding: 1.5rem;
  background: white;
  text-align: center;
}

.visualization-item .plot-container.enhanced {
  background: linear-gradient(135deg, #fafafa, #f5f5f5);
}

.clustering-plot-image {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin: 0;
  display: block;
  transition: transform 0.3s ease;
}

.clustering-plot-image:hover {
  transform: scale(1.02);
}

.visualization-item .plot-description {
  padding: 1.5rem;
  background: var(--gray-50);
  color: var(--gray-700);
  font-size: 0.875rem;
  line-height: 1.6;
  border-top: 1px solid var(--gray-200);
}

.visualization-item .plot-description.enhanced {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-top: 2px solid var(--info-color);
}

.color-green,
.color-orange,
.color-red {
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

.color-green {
  background: #dcfce7;
  color: #15803d;
}

.color-orange {
  background: #fed7aa;
  color: #c2410c;
}

.color-red {
  background: #fecaca;
  color: #dc2626;
}

/* Clustering Analysis Card Mejorada */
.clustering-analysis-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  margin-bottom: 2rem;
  border: 2px solid var(--purple-color);
  overflow: hidden;
}

.clustering-analysis-card.enhanced {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.clustering-header {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.clustering-header i {
  color: var(--purple-color);
  font-size: 1.25rem;
}

.clustering-header h3 {
  color: var(--gray-800);
  font-size: 1.25rem;
  margin: 0;
}

.clustering-content {
  padding: 2rem;
}

.cluster-assignment {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.cluster-assignment.enhanced {
  background: var(--gray-50);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.cluster-assignment h4 {
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.cluster-badge {
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  border: 2px solid;
}

.cluster-badge.enhanced {
  box-shadow: var(--shadow);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cluster-badge.red {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
  border-color: #f87171;
}

.cluster-badge.orange {
  background: linear-gradient(135deg, #fef3c7, #fed7aa);
  color: #d97706;
  border-color: #fbbf24;
}

.cluster-badge.green {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #16a34a;
  border-color: #4ade80;
}

.cluster-badge.purple {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  color: #7c3aed;
  border-color: #a855f7;
}

.position-info {
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--info-color);
}

.position-info p {
  margin: 0;
  color: var(--gray-700);
  font-weight: 500;
}

/* Información del algoritmo mejorada */
.algorithm-info {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--gray-200);
  margin-top: 2rem;
}

.algorithm-info.enhanced {
  border: 2px solid var(--info-color);
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.algorithm-info h4 {
  margin-bottom: 1.5rem;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.algorithm-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.detail-item:hover {
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.detail-label {
  font-weight: 600;
  color: var(--gray-700);
}

.detail-value {
  color: var(--gray-900);
  font-weight: 500;
  text-align: right;
}

/* Botones mejorados */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-200);
}

.btn-outline:hover {
  background: var(--gray-50);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-secondary {
  background: var(--purple-color);
  color: white;
}

.btn-secondary:hover {
  background: #7c3aed;
  transform: translateY(-2px);
}

.btn-info {
  background: var(--info-color);
  color: white;
}

.btn-info:hover {
  background: #0891b2;
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Botones de acción mejorados */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  margin-bottom: 3rem;
}

.results-actions.enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .action-buttons {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-buttons .btn {
    min-width: 220px;
  }
}

/* Notificaciones */
.notification {
  position: fixed;
  top: 100px;
  right: 1rem;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1001;
  border-left: 4px solid;
  animation: slideInRight 0.3s ease-out;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

.notification.info {
  border-left-color: var(--info-color);
}

.notification i {
  font-size: 1.25rem;
}

.notification.success i {
  color: var(--success-color);
}

.notification.error i {
  color: var(--danger-color);
}

.notification.info i {
  color: var(--info-color);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  text-align: center;
  color: var(--primary-color);
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.loading-spinner p {
  font-weight: 600;
  color: var(--gray-700);
}

/* Modales mejorados */
.details-modal,
.explanation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.large {
  max-width: 800px;
}

.modal-header {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  margin: 0;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--gray-500);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
}

.modal-close:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-body pre {
  white-space: pre-wrap;
  font-family: "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--gray-700);
  background: var(--gray-50);
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.modal-footer {
  background: var(--gray-50);
  padding: 1rem 1.5rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  border-top: 1px solid var(--gray-200);
}

/* Resto de estilos existentes... */
/* (Mantener todos los estilos del archivo original que no se han modificado) */

/* Cuestionario */
.questionnaire-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  max-width: 800px;
  margin: 0 auto;
}

.questionnaire-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 2rem;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.header-content i {
  font-size: 2rem;
}

.header-content h2 {
  font-size: 1.75rem;
  margin: 0;
}

.header-content p {
  opacity: 0.9;
  margin: 0;
}

.questionnaire-body {
  padding: 2rem;
}

/* Progreso */
.progress-section {
  margin-bottom: 2rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-600);
}

.progress-bar {
  background: var(--gray-200);
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  height: 100%;
  transition: width 0.3s ease;
  width: 0%;
}

/* Contenido de pasos */
.step-content {
  margin-bottom: 2rem;
}

.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.step-category {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-radius: 9999px;
  font-size: 0.875rem;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.critical-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

/* Tarjeta de pregunta */
.question-card {
  background: var(--gray-50);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
}

.question-card.critical {
  background: #fef2f2;
  border-color: #fecaca;
}

.question-text {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.question-description {
  color: var(--gray-600);
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Radio buttons */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.radio-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.radio-option:hover {
  background: var(--gray-50);
  border-color: var(--primary-color);
}

.radio-option.selected {
  background: #dbeafe;
  border-color: var(--primary-color);
}

.radio-input {
  margin-right: 1rem;
  width: 20px;
  height: 20px;
  accent-color: var(--primary-color);
}

.radio-label {
  flex: 1;
  color: var(--gray-700);
  cursor: pointer;
}

/* Input de edad */
.age-input-section {
  text-align: center;
}

.age-input-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.age-input-header i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

.age-description {
  color: var(--gray-600);
  margin-bottom: 2rem;
}

.age-input-group {
  max-width: 400px;
  margin: 0 auto;
}

.age-label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.age-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1.125rem;
  text-align: center;
  transition: var(--transition);
}

.age-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.age-display {
  margin-top: 1rem;
  padding: 1rem;
  background: #dbeafe;
  border-radius: var(--border-radius);
  color: #1e40af;
  font-weight: 600;
}

.age-error {
  margin-top: 1rem;
  padding: 1rem;
  background: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  color: #dc2626;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Navegación */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.back-to-home {
  text-align: center;
  margin-top: 1rem;
}

/* Análisis */
.analysis-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  margin: 0 auto;
}

.analysis-content {
  padding: 3rem 2rem;
  text-align: center;
}

.brain-loading {
  position: relative;
  display: inline-block;
  margin-bottom: 2rem;
}

.brain-loading i {
  font-size: 4rem;
  color: var(--primary-color);
}

.spinner {
  position: absolute;
  top: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  border: 3px solid transparent;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.analysis-content h2 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--gray-800);
}

.loading-steps {
  text-align: left;
  max-width: 400px;
  margin: 0 auto 2rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  color: var(--gray-700);
}

.step.completed i {
  color: var(--success-color);
}

.step.loading i {
  color: var(--primary-color);
}

.analysis-progress {
  width: 75%;
  animation: progress 2s ease-in-out;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 75%;
  }
}

.progress-note {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin-top: 1rem;
}

/* Resultados */
.results-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
}

.results-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 2rem;
  text-align: center;
}

.results-header i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.results-header h2 {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.results-header p {
  opacity: 0.9;
}

.results-body {
  padding: 2rem;
}

/* Información del análisis */
.analysis-info {
  background: var(--gray-50);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.analysis-info.enhanced {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid var(--gray-200);
}

.analysis-info-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.analysis-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  font-size: 0.875rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  color: var(--gray-600);
  margin-bottom: 0.25rem;
}

.info-value {
  font-weight: 600;
  color: var(--gray-800);
}

/* Alerta de nivel */
.alert-card {
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  border: 2px solid;
}

.alert-card.enhanced {
  box-shadow: var(--shadow);
}

.alert-card.low-risk {
  background: #f0fdf4;
  border-color: var(--success-color);
  color: #15803d;
}

.alert-card.moderate-risk {
  background: #fffbeb;
  border-color: var(--warning-color);
  color: #d97706;
}

.alert-card.high-risk {
  background: #fef2f2;
  border-color: var(--danger-color);
  color: #dc2626;
}

.alert-card.atypical {
  background: #faf5ff;
  border-color: var(--purple-color);
  color: #7c3aed;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.alert-level {
  font-size: 1.25rem;
  font-weight: bold;
}

.alert-range {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Mensaje principal */
.main-message-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.main-message-card.enhanced {
  border: 2px solid var(--primary-color);
  box-shadow: var(--shadow);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.message-header i {
  color: var(--primary-color);
}

.message-text {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.recommendation-box {
  background: #dbeafe;
  border-radius: var(--border-radius);
  padding: 1rem;
}

.recommendation-text {
  color: #1e40af;
  font-weight: 600;
}

/* Nota importante */
.important-note {
  background: #fffbeb;
  border: 1px solid #fef3c7;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.important-note.enhanced {
  border: 2px solid var(--warning-color);
  box-shadow: var(--shadow);
}

.important-note i {
  color: var(--warning-color);
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.important-note-text {
  color: #92400e;
  line-height: 1.6;
}

/* Responsive */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: var(--shadow);
    display: none;
    z-index: 999;
  }

  .nav-menu.active {
    display: flex;
  }

  .nav-toggle {
    display: block;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .info-content {
    grid-template-columns: 1fr;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .navigation-buttons .btn {
    width: 100%;
  }

  .results-actions {
    flex-direction: column;
  }

  .results-actions .btn {
    width: 100%;
  }

  .k-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .k-select-enhanced {
    width: 100%;
    min-width: auto;
  }

  .algorithm-details {
    grid-template-columns: 1fr;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .modal-footer {
    flex-direction: column;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.view.active {
  animation: fadeIn 0.3s ease-out;
}

/* Información sobre autismo y técnica - mantener estilos existentes */
.info-header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.info-header-section h1 {
  font-size: 2.5rem;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}

.info-header-section p {
  color: var(--gray-600);
  font-size: 1.25rem;
}

.info-section-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: 2rem;
}

.section-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-header.blue {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.section-header.green {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
}

.section-header.purple {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
}

.section-header.orange {
  background: linear-gradient(135deg, #ffedd5, #fed7aa);
}

.section-header i {
  font-size: 1.5rem;
}

.section-header.blue i {
  color: var(--primary-color);
}

.section-header.green i {
  color: var(--success-color);
}

.section-header.purple i {
  color: var(--purple-color);
}

.section-header.orange i {
  color: var(--warning-color);
}

.section-header h2 {
  font-size: 1.5rem;
  color: var(--gray-800);
}

.section-content {
  padding: 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.content-text h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.content-text p {
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.highlight-box {
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-top: 1.5rem;
}

.highlight-box.blue {
  background: #dbeafe;
}

.highlight-box.green {
  background: #dcfce7;
}

.highlight-box.purple {
  background: #f3e8ff;
}

.highlight-box.orange {
  background: #ffedd5;
}

.highlight-box h4 {
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.highlight-box ul {
  list-style: none;
}

.highlight-box li {
  margin-bottom: 0.5rem;
}

.content-visual {
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-image {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 300px;
  background: var(--gray-100);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  display: none;
}

.placeholder-image i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* Grupos de edad */
.age-groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.age-group-card {
  background: var(--gray-50);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
}

.age-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
}

.age-icon.blue {
  background: var(--primary-color);
}

.age-icon.green {
  background: var(--success-color);
}

.age-icon.orange {
  background: var(--warning-color);
}

.age-icon.purple {
  background: var(--purple-color);
}

.age-group-card h3 {
  font-size: 1.125rem;
  margin-bottom: 1rem;
  color: var(--gray-800);
}

.age-group-card ul {
  list-style: none;
  text-align: left;
}

.age-group-card li {
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

/* Tratamiento */
.treatment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Información técnica */
.tech-info-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
}

.tech-header {
  background: linear-gradient(135deg, var(--gray-700), var(--gray-900));
  color: white;
  padding: 2rem;
  text-align: center;
}

.tech-header i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.tech-header h2 {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.tech-header p {
  opacity: 0.9;
}

.tech-content {
  padding: 2rem;
}

.tech-section {
  margin-bottom: 3rem;
}

.tech-section h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tech-section h3 i {
  color: var(--primary-color);
}

.methodology-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.method-card {
  background: var(--gray-50);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--gray-200);
}

.method-card h4 {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
  color: var(--gray-800);
}

.method-card p {
  color: var(--gray-700);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.method-card ul {
  list-style: none;
}

.method-card li {
  margin-bottom: 0.5rem;
  color: var(--gray-600);
}

.age-adaptation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  text-align: center;
}

.adaptation-card {
  background: var(--gray-50);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid var(--gray-200);
}

.adaptation-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
}

.adaptation-card h4 {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  color: var(--gray-800);
}

.adaptation-card p {
  color: var(--gray-600);
  font-size: 0.875rem;
}

.critical-signals {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.signal-alert {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
}

.signal-alert i {
  color: var(--danger-color);
  font-size: 1.25rem;
}

.risk-levels {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.risk-level {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

.risk-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.risk-level.green .risk-indicator {
  background: var(--success-color);
}

.risk-level.orange .risk-indicator {
  background: var(--warning-color);
}

.risk-level.red .risk-indicator {
  background: var(--danger-color);
}

.risk-level.purple .risk-indicator {
  background: var(--purple-color);
}

.tech-footer {
  padding: 2rem;
  text-align: center;
  border-top: 1px solid var(--gray-200);
}

.disclaimer {
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.disclaimer i {
  color: var(--warning-color);
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.disclaimer div {
  color: var(--gray-700);
  line-height: 1.6;
}

.btn-ghost {
  background: transparent;
  color: var(--gray-600);
}

.btn-ghost:hover {
  background: var(--gray-100);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Estilos adicionales para la visualización de clustering */
.plot-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  color: var(--gray-500);
  text-align: center;
  padding: 2rem;
}

.plot-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

.error-message i {
  color: var(--danger-color);
}
