<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análisis de Clustering - Proyecto Austimo</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 2em;
            font-weight: bold;
        }

        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }

        .graphics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .graphic-card {
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .graphic-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .graphic-card img {
            width: 100%;
            height: auto;
            display: block;
        }

        .graphic-card .card-body {
            padding: 20px;
        }

        .graphic-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }

        .graphic-card p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 40px;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-top: 20px;
            cursor: pointer;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-button i {
            font-size: 1.1em;
        }

        .timestamp {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .graphics-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Análisis de Clustering - Proyecto Austimo</h1>
            <p>Análisis completo de datos con enfoque en DBSCAN como algoritmo ganador</p>
            <button class="nav-button" onclick="window.location.href='../index.html'">
                <i class="fas fa-arrow-left"></i>
                Volver al Sistema Principal
            </button>
        </div>

        <div class="content">
            <div class="timestamp">
                <strong>📅 Generado el:</strong> 11 de Junio de 2025 a las 01:51:19
            </div>

            <div class="stats">
                <div class="stat-card">
                    <h3>10</h3>
                    <p>Gráficos Generados</p>
                </div>
                <div class="stat-card">
                    <h3>DBSCAN</h3>
                    <p>Algoritmo Ganador</p>
                </div>
                <div class="stat-card">
                    <h3>datos.csv</h3>
                    <p>Dataset Utilizado</p>
                </div>
                <div class="stat-card">
                    <h3>10+</h3>
                    <p>Análisis Realizados</p>
                </div>
            </div>

            <h2 style="color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px;">
                📊 Visualizaciones Generadas
            </h2>

            <div class="graphics-grid">
                <div class="graphic-card">
                    <img src="graficos_generados/01_eda_distribuciones_numericas.png" alt="Distribución de Variables Numéricas Clave" loading="lazy">
                    <div class="card-body">
                        <h3>01. Distribución de Variables Numéricas Clave</h3>
                        <p><strong>Archivo:</strong> 01_eda_distribuciones_numericas.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/02_eda_variables_categoricas.png" alt="Distribución de Variables Categóricas" loading="lazy">
                    <div class="card-body">
                        <h3>02. Distribución de Variables Categóricas</h3>
                        <p><strong>Archivo:</strong> 02_eda_variables_categoricas.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/03_eda_matriz_correlacion.png" alt="Matriz de Correlación - Variables Numéricas" loading="lazy">
                    <div class="card-body">
                        <h3>03. Matriz de Correlación - Variables Numéricas</h3>
                        <p><strong>Archivo:</strong> 03_eda_matriz_correlacion.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/04_eda_analisis_grupos_riesgo.png" alt="Análisis por Grupos de Riesgo Verdaderos" loading="lazy">
                    <div class="card-body">
                        <h3>04. Análisis por Grupos de Riesgo Verdaderos</h3>
                        <p><strong>Archivo:</strong> 04_eda_analisis_grupos_riesgo.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/05_clustering_inicial_pca.png" alt="Clusters Iniciales - Visualización PCA" loading="lazy">
                    <div class="card-body">
                        <h3>05. Clusters Iniciales - Visualización PCA</h3>
                        <p><strong>Archivo:</strong> 05_clustering_inicial_pca.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/06_clustering_inicial_tsne.png" alt="Clusters Iniciales - Visualización t-SNE" loading="lazy">
                    <div class="card-body">
                        <h3>06. Clusters Iniciales - Visualización t-SNE</h3>
                        <p><strong>Archivo:</strong> 06_clustering_inicial_tsne.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/07_comparacion_antes_despues.png" alt="Comparación Antes vs Después - Enfoque DBSCAN" loading="lazy">
                    <div class="card-body">
                        <h3>07. Comparación Antes vs Después - Enfoque DBSCAN</h3>
                        <p><strong>Archivo:</strong> 07_comparacion_antes_despues.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/08_importancia_caracteristicas.png" alt="Importancia de Características" loading="lazy">
                    <div class="card-body">
                        <h3>08. Importancia de Características</h3>
                        <p><strong>Archivo:</strong> 08_importancia_caracteristicas.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/09_arboles_decision_comparacion.png" alt="Árboles de Decisión - Ensemble vs DBSCAN" loading="lazy">
                    <div class="card-body">
                        <h3>09. Árboles de Decisión - Ensemble vs DBSCAN</h3>
                        <p><strong>Archivo:</strong> 09_arboles_decision_comparacion.png</p>
                    </div>
                </div>
                <div class="graphic-card">
                    <img src="graficos_generados/10_analisis_clusters_final.png" alt="Análisis de Clusters Final" loading="lazy">
                    <div class="card-body">
                        <h3>10. Análisis de Clusters Final</h3>
                        <p><strong>Archivo:</strong> 10_analisis_clusters_final.png</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><strong>🎯 Proyecto de Clustering Avanzado</strong></p>
            <p>Desarrollado con Python, Scikit-learn, Matplotlib y Seaborn</p>
            <p>Dataset: PaginaGraficos/datos.csv (1,654 registros, 19 columnas)</p>
            <p>Gráficos guardados en: graficos_generados/</p>
        </div>
    </div>
</body>
</html>