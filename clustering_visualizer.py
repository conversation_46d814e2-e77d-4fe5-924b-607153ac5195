import pandas as pd
import numpy as np

# Configurar matplotlib para usar backend no interactivo ANTES de importar pyplot
import matplotlib
matplotlib.use('Agg')  # Backend no interactivo para aplicaciones web
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import RobustScaler
from sklearn.decomposition import PCA
from sklearn.neighbors import NearestNeighbors
import base64
from io import BytesIO
import os

# Configuración adicional para evitar problemas de threading
matplotlib.rcParams['figure.max_open_warning'] = 0

class ClusteringVisualizer:
    def __init__(self, data_path='datos.csv'):
        """
        Inicializa el visualizador de clustering con soporte para 2 o 3 clases y DBSCAN
        """
        self.data_path = data_path
        self.scaler = RobustScaler()
        self.pca_model = None
        self.kmeans_models = {}  # Almacenará modelos para k=2 y k=3
        self.dbscan_models = {}  # Almacenará modelos DBSCAN para diferentes configuraciones
        self.processed_data = None
        self.historical_data = None
        
        print(f"🔍 ClusteringVisualizer inicializado, buscando datos en: {self.data_path}")
    
    def load_data(self):
        """
        Carga los datos del archivo CSV
        """
        try:
            if os.path.exists(self.data_path):
                print(f"📂 Cargando datos desde: {self.data_path}")
                
                # Intentar diferentes encodings y separadores
                encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
                separators = [',', ';', '\t']
                
                for encoding in encodings:
                    for sep in separators:
                        try:
                            df = pd.read_csv(self.data_path, encoding=encoding, sep=sep)
                            if len(df.columns) > 1 and len(df) > 0:
                                print(f"✅ Datos cargados: {len(df)} filas, {len(df.columns)} columnas")
                                print(f"📊 Columnas encontradas: {list(df.columns)}")
                                
                                # Analizar distribución de datos
                                self.analyze_data_distribution(df)
                                
                                self.historical_data = df
                                return df
                        except Exception as e:
                            continue
            
            # Si no se puede cargar, generar datos de ejemplo
            print("⚠️ No se pudo cargar el archivo CSV, generando datos de ejemplo...")
            return self.generate_sample_data()
            
        except Exception as e:
            print(f"❌ Error cargando datos: {e}")
            return self.generate_sample_data()
    
    def analyze_data_distribution(self, df):
        """
        Analiza la distribución de los datos para verificar la interpretación correcta
        """
        print("\n📊 Análisis de distribución de datos:")
        
        # Verificar distribución de respuestas
        print("   Distribución de respuestas:")
        for i in range(1, 11):
            col = f'A{i}'
            if col in df.columns:
                yes_count = df[col].sum()
                no_count = len(df) - yes_count
                print(f"      • {col}: Sí={yes_count} ({yes_count/len(df)*100:.1f}%), No={no_count} ({no_count/len(df)*100:.1f}%)")
        
        # Verificar distribución de clases ASD
        if 'Class/ASD' in df.columns:
            yes_asd = df[df['Class/ASD'] == 'YES'].shape[0]
            no_asd = df[df['Class/ASD'] == 'NO'].shape[0]
            print(f"   Distribución de ASD: YES={yes_asd} ({yes_asd/len(df)*100:.1f}%), NO={no_asd} ({no_asd/len(df)*100:.1f}%)")
        
        # Calcular puntuación de riesgo para cada caso
        if all(f'A{i}' in df.columns for i in range(1, 11)):
            df['risk_score'] = 0
            for i in range(1, 10):
                df['risk_score'] += (1 - df[f'A{i}'])  # Invertido para A1-A9
            df['risk_score'] += df['A10']  # Directo para A10
            
            avg_score = df['risk_score'].mean()
            print(f"   Puntuación de riesgo promedio: {avg_score:.2f}/10")
            
            # Verificar correlación entre puntuación y diagnóstico
            if 'Class/ASD' in df.columns:
                avg_yes = df[df['Class/ASD'] == 'YES']['risk_score'].mean()
                avg_no = df[df['Class/ASD'] == 'NO']['risk_score'].mean()
                print(f"      • Promedio para ASD=YES: {avg_yes:.2f}")
                print(f"      • Promedio para ASD=NO: {avg_no:.2f}")
        
        print("✅ Análisis de distribución completado\n")
    
    def generate_sample_data(self):
        """
        Genera datos de ejemplo optimizados para clustering con grupos bien definidos
        """
        np.random.seed(42)
        n_samples = 1500
        
        # Crear grupos bien definidos para visualización clara
        # Grupo 1: Bajo riesgo (respuestas típicas) - A1-A9=1, A10=0
        low_risk_group = np.random.normal(loc=[-2, 0], scale=[0.5, 1.0], size=(600, 2))
        
        # Grupo 2: Alto riesgo (respuestas atípicas) - A1-A9=0, A10=1
        high_risk_group = np.random.normal(loc=[2, 0], scale=[0.5, 1.0], size=(500, 2))
        
        # Grupo 3: Riesgo intermedio (centro) - A1-A9 mixto, A10 mixto
        intermediate_group = np.random.normal(loc=[0, 0], scale=[0.5, 0.5], size=(300, 2))
        
        # Casos atípicos (outliers)
        outliers = np.random.uniform(low=[-4, -3], high=[4, 3], size=(100, 2))
        
        # Combinar datos
        X = np.vstack([low_risk_group, high_risk_group, intermediate_group, outliers])
        y = np.array([0]*600 + [1]*500 + [2]*300 + [-1]*100)  # Etiquetas de grupo
        
        # Crear DataFrame
        data = []
        for i in range(len(X)):
            sample = {'Case_No': i+1}
            
            # Generar características A1-A10 basadas en el grupo
            if y[i] == 0:  # Bajo riesgo
                # A1-A9: alta probabilidad de ser 1 (comportamiento típico)
                for j in range(1, 10):
                    sample[f'A{j}'] = np.random.choice([0, 1], p=[0.15, 0.85])
                # A10: baja probabilidad de ser 1 (no mira fijamente)
                sample['A10'] = np.random.choice([0, 1], p=[0.85, 0.15])
                sample['Class/ASD'] = 'NO'
                sample['True_Risk_Level'] = 'Bajo_Riesgo'
            
            elif y[i] == 1:  # Alto riesgo
                # A1-A9: alta probabilidad de ser 0 (comportamiento atípico)
                for j in range(1, 10):
                    sample[f'A{j}'] = np.random.choice([0, 1], p=[0.85, 0.15])
                # A10: alta probabilidad de ser 1 (mira fijamente)
                sample['A10'] = np.random.choice([0, 1], p=[0.15, 0.85])
                sample['Class/ASD'] = 'YES'
                sample['True_Risk_Level'] = 'Alto_Riesgo'
            
            elif y[i] == 2:  # Riesgo intermedio
                # A1-A9: probabilidades balanceadas
                for j in range(1, 10):
                    sample[f'A{j}'] = np.random.choice([0, 1], p=[0.5, 0.5])
                # A10: probabilidades balanceadas
                sample['A10'] = np.random.choice([0, 1], p=[0.5, 0.5])
                sample['Class/ASD'] = np.random.choice(['YES', 'NO'], p=[0.4, 0.6])
                sample['True_Risk_Level'] = 'Riesgo_Moderado'
            
            else:  # Casos atípicos
                # Patrones completamente aleatorios
                for j in range(1, 11):
                    sample[f'A{j}'] = np.random.choice([0, 1])
                sample['Class/ASD'] = np.random.choice(['YES', 'NO'])
                sample['True_Risk_Level'] = 'Casos_Atipicos'
            
            # Calcular score de riesgo CORRECTO
            score = sum(1 - sample[f'A{k}'] for k in range(1, 10)) + sample['A10']
            sample['Qchat-10-Score'] = score
            
            # Otras características
            sample['Age_Mons'] = np.random.randint(12, 192)
            sample['Sex'] = np.random.choice(['m', 'f'])
            sample['X_coord'] = X[i, 0]
            sample['Y_coord'] = X[i, 1]
            sample['True_Group'] = int(y[i])
            
            data.append(sample)
        
        df = pd.DataFrame(data)
        self.historical_data = df
        
        print(f"✅ Datos de ejemplo generados: {len(df)} casos")
        
        # Verificar distribución de scores por grupo
        print("📊 Distribución de scores por grupo:")
        for group_name in df['True_Risk_Level'].unique():
            group_data = df[df['True_Risk_Level'] == group_name]
            avg_score = group_data['Qchat-10-Score'].mean()
            count = len(group_data)
            asd_rate = (group_data['Class/ASD'] == 'YES').mean() * 100
            print(f"   • {group_name}: {count} casos, Score promedio: {avg_score:.1f}, ASD: {asd_rate:.1f}%")
        
        return df
    
    def preprocess_data(self, df):
        """
        Preprocesa los datos para clustering
        """
        print("🔧 Preprocesando datos para clustering...")
        
        # Seleccionar características para clustering
        feature_cols = [f'A{i}' for i in range(1, 11)] + ['Age_Mons']
        
        # Verificar que las columnas existen
        available_cols = [col for col in feature_cols if col in df.columns]
        if not available_cols:
            print("❌ No se encontraron columnas de características esperadas")
            return None
        
        print(f"📊 Características para clustering: {available_cols}")
        
        # Extraer matriz de características
        X = df[available_cols].values
        
        # Escalado robusto
        X_scaled = self.scaler.fit_transform(X)
        
        # PCA para visualización (2D)
        self.pca_model = PCA(n_components=2, random_state=42)
        X_pca = self.pca_model.fit_transform(X_scaled)
        
        self.processed_data = {
            'X_scaled': X_scaled,
            'X_pca': X_pca,
            'feature_names': available_cols,
            'original_data': df
        }
        
        print(f"✅ Preprocesamiento completado: {X_scaled.shape}")
        return self.processed_data
    
    def find_optimal_eps(self, X, k=5):
        """
        Encuentra el valor óptimo de eps para DBSCAN usando el método del codo
        """
        neigh = NearestNeighbors(n_neighbors=k)
        neigh.fit(X)
        distances, _ = neigh.kneighbors(X)
        distances = np.sort(distances[:, k-1])
        
        # Calcular la pendiente
        slopes = np.diff(distances)
        
        # Encontrar el punto de inflexión (método del codo)
        knee_point = np.argmax(slopes) + 1
        eps = distances[knee_point]
        
        return eps
    
    def train_dbscan_models(self):
        """
        Entrena modelos DBSCAN para diferentes configuraciones
        """
        if not self.processed_data:
            print("❌ Datos no procesados")
            return None
        
        X = self.processed_data['X_scaled']
        
        print("🎯 Entrenando modelos DBSCAN para diferentes configuraciones...")
        
        # Encontrar eps óptimo
        eps_optimal = self.find_optimal_eps(X)
        print(f"📏 Eps óptimo calculado: {eps_optimal:.4f}")
        
        # Configuración para 2 clusters principales
        dbscan_2 = DBSCAN(eps=eps_optimal*0.9, min_samples=3)
        dbscan_2.fit(X)
        self.dbscan_models[2] = dbscan_2
        
        labels_2 = dbscan_2.labels_
        n_clusters_2 = len(set(labels_2)) - (1 if -1 in labels_2 else 0)
        print(f"🔍 DBSCAN (config 2) encontró {n_clusters_2} clusters y {np.sum(labels_2 == -1)} puntos de ruido")
        
        # Configuración para 3 clusters principales
        dbscan_3 = DBSCAN(eps=eps_optimal*1.1, min_samples=5)
        dbscan_3.fit(X)
        self.dbscan_models[3] = dbscan_3
        
        labels_3 = dbscan_3.labels_
        n_clusters_3 = len(set(labels_3)) - (1 if -1 in labels_3 else 0)
        print(f"🔍 DBSCAN (config 3) encontró {n_clusters_3} clusters y {np.sum(labels_3 == -1)} puntos de ruido")
        
        print("✅ Modelos DBSCAN entrenados")
        return self.dbscan_models

    def _force_two_clusters(self, X, original_labels):
        """
        Fuerza la creación de exactamente 2 clusters basados en score de riesgo
        """
        print("🔧 Forzando 2 clusters basados en score de riesgo...")

        # Calcular scores de riesgo para todos los casos
        df = self.processed_data['original_data']
        risk_scores = []

        for i in range(len(df)):
            score = 0
            # A1-A9: invertidas (0=sí, 1=no) -> score += (1-valor)
            for j in range(1, 10):
                if f'A{j}' in df.columns:
                    score += (1 - df.iloc[i][f'A{j}'])
            # A10: directa (1=sí) -> score += valor
            if 'A10' in df.columns:
                score += df.iloc[i]['A10']
            risk_scores.append(score)

        risk_scores = np.array(risk_scores)

        # Analizar distribución de scores
        unique_scores = np.unique(risk_scores)
        print(f"📊 Scores únicos encontrados: {unique_scores}")

        # Crear nuevas etiquetas basadas en score de riesgo usando mediana
        new_labels = np.zeros(len(risk_scores), dtype=int)

        # Usar mediana como punto de corte para dividir en 2 grupos
        median_score = np.median(risk_scores)
        print(f"📊 Mediana de scores: {median_score:.1f}")

        # Grupo 0: Bajo riesgo (score <= mediana)
        low_risk_mask = risk_scores <= median_score
        new_labels[low_risk_mask] = 0

        # Grupo 1: Alto riesgo (score > mediana)
        high_risk_mask = risk_scores > median_score
        new_labels[high_risk_mask] = 1

        # Verificar distribución final
        low_count = np.sum(new_labels == 0)
        high_count = np.sum(new_labels == 1)

        print(f"📊 Distribución final por riesgo:")
        print(f"   • Bajo riesgo (0): {low_count} casos ({low_count/len(risk_scores)*100:.1f}%)")
        print(f"   • Alto riesgo (1): {high_count} casos ({high_count/len(risk_scores)*100:.1f}%)")

        # Verificar scores promedio por grupo
        if low_count > 0:
            avg_low = np.mean(risk_scores[new_labels == 0])
            print(f"   • Score promedio bajo riesgo: {avg_low:.1f}")
        if high_count > 0:
            avg_high = np.mean(risk_scores[new_labels == 1])
            print(f"   • Score promedio alto riesgo: {avg_high:.1f}")

        return new_labels

    def predict_user_cluster(self, user_responses, k=3):
        """
        Predice el cluster para un nuevo usuario
        """
        if not self.processed_data or k not in self.dbscan_models:
            print(f"❌ Modelo DBSCAN para k={k} no disponible")
            return None

        try:
            # Calcular score de riesgo del usuario
            user_risk_score = 0
            for i in range(1, 10):
                if f'A{i}' in user_responses:
                    user_risk_score += (1 - user_responses[f'A{i}'])
            if 'A10' in user_responses:
                user_risk_score += user_responses['A10']

            print(f"📊 Score de riesgo del usuario: {user_risk_score}/10")

            # Para K=2, usar la misma lógica que _force_two_clusters
            if k == 2:
                # Calcular mediana de scores de todos los datos para mantener consistencia
                df = self.processed_data['original_data']
                all_risk_scores = []

                for i in range(len(df)):
                    score = 0
                    # A1-A9: invertidas (0=sí, 1=no) -> score += (1-valor)
                    for j in range(1, 10):
                        if f'A{j}' in df.columns:
                            score += (1 - df.iloc[i][f'A{j}'])
                    # A10: directa (1=sí) -> score += valor
                    if 'A10' in df.columns:
                        score += df.iloc[i]['A10']
                    all_risk_scores.append(score)

                median_score = np.median(all_risk_scores)
                print(f"📊 Mediana de scores del dataset: {median_score:.1f}")

                # Asignar cluster basado en la misma lógica que _force_two_clusters
                if user_risk_score <= median_score:
                    predicted_cluster = 0  # Bajo riesgo
                    print(f"🎯 Usuario asignado a cluster 0 (Bajo riesgo) - Score: {user_risk_score} <= Mediana: {median_score}")
                else:
                    predicted_cluster = 1  # Alto riesgo
                    print(f"🎯 Usuario asignado a cluster 1 (Alto riesgo) - Score: {user_risk_score} > Mediana: {median_score}")

                # Preparar datos del usuario para obtener posición PCA
                feature_names = self.processed_data['feature_names']
                user_data = []

                for feature in feature_names:
                    if feature in user_responses:
                        user_data.append(user_responses[feature])
                    else:
                        user_data.append(0)

                user_array = np.array(user_data).reshape(1, -1)
                user_scaled = self.scaler.transform(user_array)
                user_pca = self.pca_model.transform(user_scaled)

                return {
                    'cluster_id': int(predicted_cluster),
                    'user_position_pca': [float(x) for x in user_pca[0]],
                    'user_risk_score': user_risk_score
                }

            # Para K=3, usar la lógica original
            # Preparar datos del usuario para clustering
            feature_names = self.processed_data['feature_names']
            user_data = []

            for feature in feature_names:
                if feature in user_responses:
                    user_data.append(user_responses[feature])
                else:
                    user_data.append(0)

            user_array = np.array(user_data).reshape(1, -1)
            user_scaled = self.scaler.transform(user_array)
            user_pca = self.pca_model.transform(user_scaled)

            # Encontrar cluster más cercano usando DBSCAN
            X_scaled = self.processed_data['X_scaled']
            labels = self.dbscan_models[k].labels_

            from sklearn.metrics import pairwise_distances
            distances = pairwise_distances(user_scaled, X_scaled).flatten()

            # Encontrar vecinos más cercanos
            nearest_indices = np.argsort(distances)[:5]
            nearest_labels = labels[nearest_indices]

            # Determinar cluster basado en vecinos y k
            if np.sum(nearest_labels == -1) >= 3:
                # Usuario parece ser atípico
                if k == 3:
                    # Para K=3: Asignar al cluster más apropiado según score de riesgo
                    if user_risk_score <= 3:
                        predicted_cluster = 0  # Sin autismo
                    elif user_risk_score <= 6:
                        predicted_cluster = 1  # Riesgo intermedio
                    else:
                        predicted_cluster = 2  # Con autismo
                    print(f"🎯 Usuario atípico asignado por score de riesgo para K=3: cluster {predicted_cluster}")
                else:
                    predicted_cluster = -1
            else:
                valid_labels = nearest_labels[nearest_labels != -1]
                if len(valid_labels) > 0:
                    from collections import Counter
                    predicted_cluster = Counter(valid_labels).most_common(1)[0][0]
                else:
                    predicted_cluster = -1

            return {
                'cluster_id': int(predicted_cluster),
                'user_position_pca': [float(x) for x in user_pca[0]],
                'user_risk_score': user_risk_score
            }

        except Exception as e:
            print(f"❌ Error en predicción: {e}")
            return None
    
    def create_clustering_visualization(self, user_prediction=None, k=3):
        """
        Crea visualización de clustering con k clases y posición del usuario
        """
        if not self.processed_data or k not in self.dbscan_models:
            print(f"❌ Modelo DBSCAN para k={k} no disponible")
            return None
        
        X_pca = self.processed_data['X_pca']
        df = self.processed_data['original_data']
        
        # Obtener etiquetas de cluster usando DBSCAN
        labels = self.dbscan_models[k].labels_.copy()

        # MODIFICACIÓN: Manejar casos atípicos según k
        if k == 2:
            # Para K=2: Forzar exactamente 2 clusters basados en score de riesgo
            print("🔧 Aplicando clustering K=2 basado en score de riesgo...")

            # Recalcular clusters usando score de riesgo para K=2
            labels = self._force_two_clusters(None, labels)

            print(f"📊 K=2: Todos los {len(labels)} casos asignados a 2 clusters basados en score de riesgo")

        elif k == 3:
            # Para K=3: Eliminar casos atípicos completamente de la visualización
            noise_mask = labels == -1
            if np.any(noise_mask):
                print(f"🚫 Ocultando {np.sum(noise_mask)} casos atípicos para K=3 (solo mostrar 3 grupos bien definidos)")
                # Los casos atípicos se mantendrán como -1 pero no se mostrarán

        # Cerrar cualquier figura anterior para evitar problemas de memoria
        plt.close('all')

        # Crear figura
        fig, ax = plt.subplots(figsize=(12, 8))

        # Definir colores y etiquetas según k
        if k == 2:
            colors = ['#4CAF50', '#F44336']  # Verde (sin autismo), Rojo (con autismo)
            cluster_labels = ['Sin Autismo', 'Con Autismo']
        else:  # k=3
            colors = ['#4CAF50', '#FF9800', '#F44336']  # Verde, Naranja, Rojo
            cluster_labels = ['Sin Autismo', 'Riesgo Intermedio', 'Con Autismo']
        
        # Plotear puntos por cluster para DBSCAN
        unique_labels = set(labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        
        print(f"📊 Visualizando {n_clusters} clusters y {np.sum(labels == -1)} puntos de ruido")
        
        # Ordenar clusters por posición en PCA (eje X)
        cluster_centers = []
        for c in unique_labels:
            if c != -1:  # Excluir ruido
                mask = labels == c
                if np.any(mask):
                    center_x = np.mean(X_pca[mask, 0])
                    cluster_centers.append((c, center_x))
        
        # Ordenar por posición X
        cluster_centers.sort(key=lambda x: x[1])
        
        # Crear mapeo de etiquetas
        label_mapping = {}
        for i, (c, _) in enumerate(cluster_centers):
            label_mapping[c] = i
        
        # Asegurar que tenemos suficientes colores
        while len(colors) < len(cluster_centers):
            colors.append(f'#{np.random.randint(0, 16777215):06x}')
            cluster_labels.append(f'Cluster {len(colors)}')
        
        # Plotear puntos por cluster
        for i, (c, _) in enumerate(cluster_centers):
            mask = labels == c
            plt.scatter(X_pca[mask, 0], X_pca[mask, 1],
                       c=colors[i], alpha=0.7, s=60,
                       label=f'{cluster_labels[i]} ({np.sum(mask)} casos)')

        # Plotear puntos de ruido (casos atípicos) SOLO para K=2
        if k == 2:
            noise_mask = labels == -1
            if np.any(noise_mask):
                plt.scatter(X_pca[noise_mask, 0], X_pca[noise_mask, 1],
                           c='#9e9e9e', alpha=0.5, s=40, marker='x',
                           label=f'Casos Atípicos ({np.sum(noise_mask)} casos)')
        # Para K=3: No mostrar casos atípicos (ya están ocultos)
        
        # Agregar usuario actual si está disponible
        if user_prediction:
            user_pos = user_prediction['user_position_pca']
            cluster_id = user_prediction['cluster_id']
            
            plt.scatter(user_pos[0], user_pos[1], 
                       c='yellow', s=300, marker='*', 
                       edgecolors='black', linewidth=2,
                       label='Su Posición', zorder=10)
            
            # Añadir círculo alrededor del usuario
            circle = plt.Circle((user_pos[0], user_pos[1]), 
                              radius=0.3, fill=False, color='black', 
                              linestyle='--', linewidth=2, alpha=0.8)
            plt.gca().add_patch(circle)
            
            # Añadir texto indicando el grupo
            if cluster_id == -1:
                if k == 2:
                    group_text = 'Caso Atípico'
                else:  # k == 3
                    # Para K=3, no debería haber casos atípicos, pero por seguridad
                    group_text = 'Riesgo Intermedio'
            elif cluster_id in label_mapping:
                mapped_id = label_mapping[cluster_id]
                if mapped_id < len(cluster_labels):
                    group_text = cluster_labels[mapped_id]
                else:
                    group_text = f'Cluster {cluster_id}'
            else:
                # Mapeo directo para casos donde no está en label_mapping
                if k == 2:
                    group_text = 'Sin Autismo' if cluster_id == 0 else 'Con Autismo'
                else:  # k == 3
                    if cluster_id == 0:
                        group_text = 'Sin Autismo'
                    elif cluster_id == 1:
                        group_text = 'Riesgo Intermedio'
                    else:
                        group_text = 'Con Autismo'
            
            plt.annotate(f'Su posición: {group_text}', 
                        xy=(user_pos[0], user_pos[1]),
                        xytext=(user_pos[0], user_pos[1] + 0.5),
                        ha='center', va='bottom',
                        bbox=dict(boxstyle="round,pad=0.3", fc="yellow", ec="black", alpha=0.8),
                        arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0.2"))
        
        # Configurar gráfico
        plt.xlabel('Componente Principal 1')
        plt.ylabel('Componente Principal 2')
        plt.title(f'Clustering DBSCAN con {k} Grupos de Riesgo de Autismo', fontsize=16)
        plt.grid(True, alpha=0.3)
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=min(5, len(cluster_labels) + 1))
        
        # Añadir flechas y etiquetas para orientación
        if k == 2:
            plt.annotate('Sin Autismo', xy=(-3, -2), xytext=(-3, -2.5), 
                        ha='center', fontsize=14, fontweight='bold', color='#4CAF50',
                        arrowprops=dict(arrowstyle="->", color='#4CAF50'))
            plt.annotate('Con Autismo', xy=(3, -2), xytext=(3, -2.5), 
                        ha='center', fontsize=14, fontweight='bold', color='#F44336',
                        arrowprops=dict(arrowstyle="->", color='#F44336'))
        else:
            plt.annotate('Sin Autismo', xy=(-3, -2), xytext=(-3, -2.5), 
                        ha='center', fontsize=14, fontweight='bold', color='#4CAF50',
                        arrowprops=dict(arrowstyle="->", color='#4CAF50'))
            plt.annotate('Riesgo Intermedio', xy=(0, -2), xytext=(0, -2.5), 
                        ha='center', fontsize=14, fontweight='bold', color='#FF9800',
                        arrowprops=dict(arrowstyle="->", color='#FF9800'))
            plt.annotate('Con Autismo', xy=(3, -2), xytext=(3, -2.5), 
                        ha='center', fontsize=14, fontweight='bold', color='#F44336',
                        arrowprops=dict(arrowstyle="->", color='#F44336'))
        
        plt.tight_layout()

        # Convertir a base64
        buffer = BytesIO()
        try:
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{image_base64}"
        finally:
            # Asegurar que se cierre la figura y se libere la memoria
            plt.close('all')
            buffer.close()
    
    def run_visualization(self, user_responses, k=3):
        """
        Ejecuta el flujo completo de visualización
        """
        print(f"🧠 Iniciando visualización de clustering con DBSCAN k={k}...")
        
        try:
            # 1. Cargar datos
            df = self.load_data()
            if df is None:
                return {'success': False, 'error': 'No se pudieron cargar los datos'}
            
            # 2. Preprocesar datos
            processed_data = self.preprocess_data(df)
            if processed_data is None:
                return {'success': False, 'error': 'Error en el preprocesamiento'}
            
            # 3. Entrenar modelos DBSCAN
            models = self.train_dbscan_models()
            if models is None:
                return {'success': False, 'error': 'Error entrenando modelos DBSCAN'}
            
            # 4. Predecir cluster del usuario
            user_prediction = self.predict_user_cluster(user_responses, k)
            if user_prediction is None:
                return {'success': False, 'error': 'Error en la predicción del usuario'}
            
            # 5. Crear visualización
            visualization = self.create_clustering_visualization(user_prediction, k)
            if visualization is None:
                return {'success': False, 'error': 'Error creando visualización'}
            
            return {
                'success': True,
                'visualization': visualization,
                'user_prediction': user_prediction,
                'k': k
            }
            
        except Exception as e:
            print(f"❌ Error en run_visualization: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': f'Error inesperado: {str(e)}'}

# Función para integrar con la aplicación Flask existente
def generate_clustering_visualization(user_responses, k=3):
    """
    Genera visualización de clustering para integrar con la aplicación Flask
    """
    visualizer = ClusteringVisualizer()
    return visualizer.run_visualization(user_responses, k)

# Ejemplo de uso
if __name__ == "__main__":
    # Datos de ejemplo de un usuario con alto riesgo
    user_data_high_risk = {
        'A1': 0, 'A2': 0, 'A3': 0, 'A4': 0, 'A5': 0,
        'A6': 0, 'A7': 0, 'A8': 0, 'A9': 0, 'A10': 1,
        'Age_Mons': 48
    }
    
    # Datos de ejemplo de un usuario con bajo riesgo
    user_data_low_risk = {
        'A1': 1, 'A2': 1, 'A3': 1, 'A4': 1, 'A5': 1,
        'A6': 1, 'A7': 1, 'A8': 1, 'A9': 1, 'A10': 0,
        'Age_Mons': 48
    }
    
    # Generar visualizaciones para k=2 y k=3 con DBSCAN
    visualizer = ClusteringVisualizer()
    
    print("🔴 Probando caso de ALTO RIESGO:")
    result_high_k2 = visualizer.run_visualization(user_data_high_risk, k=2)
    result_high_k3 = visualizer.run_visualization(user_data_high_risk, k=3)
    
    print("\n🟢 Probando caso de BAJO RIESGO:")
    result_low_k2 = visualizer.run_visualization(user_data_low_risk, k=2)
    result_low_k3 = visualizer.run_visualization(user_data_low_risk, k=3)
    
    if all(result.get('success', False) for result in [result_high_k2, result_high_k3, result_low_k2, result_low_k3]):
        print("✅ Todas las visualizaciones generadas correctamente")
    else:
        print("❌ Error en algunas visualizaciones")
