@echo off
echo ========================================
echo  EJECUTOR CON PROGRESO - AUSTIMO
echo ========================================
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Verificacion rapida de Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python no detectado
    echo   Ejecuta: Instalador\verificar_e_instalar.bat
    cd Instalador
    pause
    exit /b 1
)

echo ✓ Iniciando proyecto...
echo.

REM PASO 1: Generar graficos (CON SALIDA VISIBLE)
echo ========================================
echo  [1/4] GENERANDO GRAFICOS
echo ========================================
echo.
echo NOTA: Este proceso puede tomar 1-2 minutos
echo       Veras la salida del script Python a continuacion:
echo.
cd PaginaGraficos
call generar_graficos.bat
if errorlevel 1 (
    echo.
    echo ✗ Error generando graficos
    cd ..\Instalador
    pause
    exit /b 1
)
cd ..
echo.
echo ✓ Graficos generados exitosamente
echo.

REM PASO 2: Ejecutar clustering_visualizer.py (CON SALIDA VISIBLE)
echo ========================================
echo  [2/4] EJECUTANDO CLUSTERING VISUALIZER
echo ========================================
echo.
python clustering_visualizer.py
if errorlevel 1 (
    echo.
    echo ✗ Error en clustering visualizer
    cd Instalador
    pause
    exit /b 1
)
echo.
echo ✓ Clustering completado exitosamente
echo.

REM PASO 3: Iniciar servidor web
echo ========================================
echo  [3/4] INICIANDO SERVIDOR WEB
echo ========================================
echo.
echo Iniciando app.py en segundo plano...
start /B python app.py
echo ✓ Servidor iniciado
echo.

REM PASO 4: Esperar y abrir navegador
echo ========================================
echo  [4/4] ABRIENDO NAVEGADOR
echo ========================================
echo.
echo Esperando 5 segundos para que el servidor se inicie completamente...

REM Mostrar cuenta regresiva
for /L %%i in (5,-1,1) do (
    echo          %%i segundos restantes...
    timeout /t 1 /nobreak >nul
)

echo.
echo Abriendo navegador...
start "" "index.html"
echo ✓ Navegador abierto
echo.

echo ========================================
echo  PROYECTO EJECUTADO EXITOSAMENTE
echo ========================================
echo.
echo ✓ Graficos generados en PaginaGraficos\graficos_generados\
echo ✓ Scripts Python ejecutados correctamente
echo ✓ Servidor web funcionando en segundo plano
echo ✓ Pagina web abierta en el navegador
echo.
echo NOTA: Para detener el servidor, cierra esta ventana
echo.
cd Instalador
pause
