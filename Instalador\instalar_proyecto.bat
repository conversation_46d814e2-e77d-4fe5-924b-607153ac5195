@echo off
echo ========================================
echo  INSTALADOR PROYECTO AUSTIMO
echo ========================================
echo.
echo Instalando dependencias necesarias...
echo.

REM Verificar si Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no esta instalado en el sistema
    echo Por favor instala Python desde https://python.org
    echo.
    pause
    exit /b 1
)

echo Python detectado correctamente
echo.

REM Actualizar pip
echo Actualizando pip...
python -m pip install --upgrade pip

REM Instalar dependencias principales
echo.
echo Instalando dependencias principales...
pip install pandas numpy matplotlib seaborn scikit-learn scipy

REM Instalar dependencias adicionales
echo.
echo Instalando dependencias adicionales...
pip install joblib h5py flask plotly dash

REM Verificar instalaciones
echo.
echo ========================================
echo  VERIFICANDO INSTALACIONES
echo ========================================
echo.

python -c "import pandas; print('✓ pandas instalado')" 2>nul || echo "✗ Error con pandas"
python -c "import numpy; print('✓ numpy instalado')" 2>nul || echo "✗ Error con numpy"
python -c "import matplotlib; print('✓ matplotlib instalado')" 2>nul || echo "✗ Error con matplotlib"
python -c "import seaborn; print('✓ seaborn instalado')" 2>nul || echo "✗ Error con seaborn"
python -c "import sklearn; print('✓ scikit-learn instalado')" 2>nul || echo "✗ Error con scikit-learn"
python -c "import scipy; print('✓ scipy instalado')" 2>nul || echo "✗ Error con scipy"
python -c "import flask; print('✓ flask instalado')" 2>nul || echo "✗ Error con flask"
python -c "import plotly; print('✓ plotly instalado')" 2>nul || echo "✗ Error con plotly"

echo.
echo ========================================
echo  INSTALACION COMPLETADA
echo ========================================
echo.
echo Todas las dependencias han sido instaladas.
echo Ahora puedes ejecutar: ejecutar_proyecto.bat
echo.
pause
