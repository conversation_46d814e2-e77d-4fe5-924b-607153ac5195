========================================
  INSTALADOR PROYECTO AUSTIMO
========================================

CONTENIDO DE ESTA CARPETA:
- verificar_e_instalar.bat  : Verifica e instala dependencias automáticamente
- ejecutar_proyecto.bat     : Ejecuta todo el proyecto completo
- README.txt               : Este archivo de instrucciones

========================================
  INSTRUCCIONES DE USO
========================================

🚀 PRIMERA VEZ (INSTALACIÓN):
1. Haz doble clic en: verificar_e_instalar.bat
2. Espera a que termine la verificación/instalación
3. Haz doble clic en: ejecutar_proyecto.bat

⚡ EJECUCIONES POSTERIORES:
- Haz doble clic en: ejecutar_proyecto.bat

🔍 SI HAY PROBLEMAS:
- Haz doble clic en: verificar_e_instalar.bat

========================================
  ESTRUCTURA DEL PROYECTO
========================================

austimo/
├── Instalador/                    ← ESTA CARPETA
│   ├── instalar_proyecto.bat
│   ├── verificar_dependencias.bat
│   ├── ejecutar_proyecto.bat
│   ├── ejecutar_rapido.bat
│   └── README.txt
├── datos.csv                      ← Datos principales
├── index.html                     ← Página web principal
├── clustering_visualizer.py       ← Script de clustering
├── app.py                         ← Servidor web
└── PaginaGraficos/
    ├── graficostablas.py
    ├── generar_graficos.bat
    └── graficos_generados/

========================================
  QUE HACE CADA ARCHIVO
========================================

verificar_e_instalar.bat:
- Verifica que Python esté instalado
- Verifica que todas las librerías estén instaladas
- Si falta algo, lo instala automáticamente
- Verifica que todos los archivos del proyecto existan

ejecutar_proyecto.bat:
- [1/4] Ejecuta ../PaginaGraficos/generar_graficos.bat
- [2/4] Ejecuta ../clustering_visualizer.py
- [3/4] Ejecuta ../app.py en segundo plano
- [4/4] Espera 8 segundos y abre ../index.html en el navegador

========================================
  SOLUCIÓN DE PROBLEMAS
========================================

❌ Si algo no funciona:
1. Ejecuta: verificar_dependencias.bat
2. Si faltan dependencias: instalar_proyecto.bat
3. Si persisten errores: revisa que todos los archivos estén presentes

📋 Archivos necesarios en la carpeta padre:
- datos.csv
- index.html
- clustering_visualizer.py
- app.py
- PaginaGraficos/graficostablas.py
- PaginaGraficos/generar_graficos.bat

========================================
  NOTAS IMPORTANTES
========================================

✅ Requiere Python instalado en el sistema
✅ Los gráficos se generan en ../PaginaGraficos/graficos_generados/
✅ El servidor web se ejecuta en segundo plano
✅ Para detener el servidor, cierra la ventana de comandos
✅ La página web se abre automáticamente en el navegador
✅ Todos los archivos .bat funcionan desde esta carpeta

========================================
