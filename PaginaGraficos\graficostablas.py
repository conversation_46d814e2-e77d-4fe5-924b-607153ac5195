# ============================================================================
# PROYECTO COMPLETO DE CLUSTERING - ANÁLISIS ACADÉMICO
# Autor: Sistema de Clustering Avanzado
# Fecha: 2024
# Objetivo: Clustering de datos CSV con análisis completo y mejoras
# CORRECCIÓN: Optimización mejorada de DBSCAN
# ============================================================================

# 1. INSTALACIÓN E IMPORTACIÓN DE LIBRERÍAS
import warnings
warnings.filterwarnings('ignore')

# Librerías básicas
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

# Clustering y Machine Learning
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering, SpectralClustering
from sklearn.mixture import GaussianMixture
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.neighbors import NearestNeighbors

# Métricas de evaluación
from sklearn.metrics import (silhouette_score, calinski_harabasz_score,
                           davies_bouldin_score, adjusted_rand_score,
                           normalized_mutual_info_score, accuracy_score)

# Análisis estadístico
from scipy.stats import f_oneway, chi2_contingency
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import pdist, squareform

# Guardado de modelos
import pickle
import joblib
import json

# h5py es opcional - solo para guardado avanzado
try:
    import h5py
    HAS_H5PY = True
except ImportError:
    HAS_H5PY = False
    print("⚠️ h5py no disponible - se omitirá guardado en formato HDF5")

# Para Google Colab
try:
    from google.colab import drive, files
    IN_COLAB = True
except ImportError:
    IN_COLAB = False

# Configuración de visualización
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# Crear carpeta para guardar gráficos
GRAFICOS_DIR = "graficos_generados"
if not os.path.exists(GRAFICOS_DIR):
    os.makedirs(GRAFICOS_DIR)
    print(f"� Carpeta creada: {GRAFICOS_DIR}")

# Configurar matplotlib para mejor calidad
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'
plt.rcParams['savefig.facecolor'] = 'white'

print("�🚀 PROYECTO DE CLUSTERING - ANÁLISIS ACADÉMICO COMPLETO")
print("=" * 70)
print(f"📅 Iniciado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("📋 Objetivo: Clustering de datos CSV con análisis y mejoras")
print("🎯 DBSCAN como algoritmo GANADOR con optimización mejorada")
print(f"💾 Gráficos se guardarán en: {GRAFICOS_DIR}/")

# Ruta específica del archivo de datos (relativa al script)
DATOS_CSV_PATH = "datos.csv"
print(f"📊 Datos desde: {DATOS_CSV_PATH}")

def save_plot(filename, dpi=300):
    """Función auxiliar para guardar gráficos con nombre consistente"""
    filepath = os.path.join(GRAFICOS_DIR, filename)
    plt.savefig(filepath, dpi=dpi, bbox_inches='tight', facecolor='white')
    print(f"💾 Gráfico guardado: {filename}")
    return filepath

# ============================================================================
# 2. BÚSQUEDA Y CARGA DE DATOS
# ============================================================================

def mount_google_drive():
    """Monta Google Drive si estamos en Colab"""
    if IN_COLAB:
        try:
            print("\n📂 Montando Google Drive...")
            drive.mount('/content/drive')
            print("✅ Google Drive montado correctamente")
            return True
        except Exception as e:
            print(f"❌ Error al montar Google Drive: {e}")
            return False
    else:
        print("ℹ️ No estamos en Google Colab, omitiendo montaje de Drive")
        return False

def find_datos_csv():
    """
    Busca específicamente el archivo datos.csv en la ruta:
    Unidades Compartidas/*/Poma Dolores/Código/datos.csv
    """
    print("\n" + "="*60)
    print("🔍 BUSCANDO ARCHIVO ESPECÍFICO: datos.csv")
    print("="*60)
    print("📂 Ruta esperada: Unidades Compartidas/*/Poma Dolores/Código/datos.csv")

    if not IN_COLAB:
        print("❌ No estamos en Google Colab, no se puede buscar en Drive")
        return None

    # Ruta base de unidades compartidas
    shared_drives_path = '/content/drive/Shareddrives'

    if not os.path.exists(shared_drives_path):
        print("❌ No se encontraron unidades compartidas")
        return None

    try:
        # Listar todas las unidades compartidas
        shared_drives = os.listdir(shared_drives_path)
        print(f"📁 Unidades compartidas encontradas: {len(shared_drives)}")

        for drive_name in shared_drives:
            print(f"\n🔍 Explorando unidad: {drive_name}")
            drive_path = os.path.join(shared_drives_path, drive_name)

            # Buscar carpeta Poma Dolores
            poma_path = os.path.join(drive_path, 'Poma Dolores')
            if os.path.exists(poma_path):
                print(f"   ✅ Encontrada carpeta: Poma Dolores")

                # Buscar carpeta Código
                codigo_path = os.path.join(poma_path, 'Código')
                if os.path.exists(codigo_path):
                    print(f"   ✅ Encontrada carpeta: Código")

                    # Buscar archivo datos.csv
                    datos_path = os.path.join(codigo_path, 'datos.csv')
                    if os.path.exists(datos_path):
                        print(f"   🎯 ¡ARCHIVO ENCONTRADO!: datos.csv")
                        print(f"   📍 Ruta completa: {datos_path}")

                        # Verificar que el archivo tiene contenido
                        try:
                            file_size = os.path.getsize(datos_path)
                            print(f"   📊 Tamaño del archivo: {file_size:,} bytes")

                            if file_size > 0:
                                return datos_path
                            else:
                                print(f"   ⚠️ El archivo está vacío")
                        except Exception as e:
                            print(f"   ❌ Error verificando archivo: {e}")
                    else:
                        print(f"   ❌ No se encontró datos.csv en Código")
                        # Listar archivos disponibles
                        try:
                            files_in_codigo = os.listdir(codigo_path)
                            csv_files = [f for f in files_in_codigo if f.lower().endswith('.csv')]
                            if csv_files:
                                print(f"   📄 Archivos CSV encontrados: {csv_files}")

                                # Si hay otros CSV, sugerir usar el primero
                                if csv_files:
                                    alt_csv_path = os.path.join(codigo_path, csv_files[0])
                                    print(f"   💡 Sugerencia: Usar {csv_files[0]} como alternativa")
                                    return alt_csv_path
                            else:
                                print(f"   📄 No hay archivos CSV en esta carpeta")
                        except Exception as e:
                            print(f"   ❌ Error listando archivos: {e}")
                else:
                    print(f"   ❌ No se encontró carpeta: Código")
            else:
                print(f"   ❌ No se encontró carpeta: Poma Dolores")

    except Exception as e:
        print(f"❌ Error explorando unidades compartidas: {e}")

    print(f"\n❌ ARCHIVO datos.csv NO ENCONTRADO en ninguna unidad compartida")
    return None

def upload_csv_file():
    """Permite al usuario subir un archivo CSV manualmente"""
    print("\n" + "="*60)
    print("📤 SUBIR ARCHIVO CSV MANUALMENTE")
    print("="*60)
    print("Como no se encontró el archivo datos.csv en la ubicación esperada,")
    print("puedes subir tu archivo CSV manualmente desde tu computadora.")

    if not IN_COLAB:
        print("❌ No estamos en Google Colab, no se puede usar la función de subida")
        return None

    print("\n📋 INSTRUCCIONES:")
    print("1. Haz clic en 'Elegir archivos' cuando aparezca")
    print("2. Selecciona tu archivo CSV")
    print("3. Espera a que se complete la subida")

    try:
        uploaded = files.upload()

        if uploaded:
            # Obtener el primer archivo subido
            filename = list(uploaded.keys())[0]
            print(f"\n✅ Archivo subido exitosamente: {filename}")

            # Verificar que es un archivo CSV
            if filename.lower().endswith('.csv'):
                file_path = f"/content/{filename}"
                file_size = len(uploaded[filename])
                print(f"📊 Tamaño: {file_size:,} bytes")
                print(f"📍 Ubicación temporal: {file_path}")
                return file_path
            else:
                print(f"❌ Error: El archivo debe ser un CSV (.csv)")
                print(f"   Archivo subido: {filename}")
                return None
        else:
            print("❌ No se subió ningún archivo")
            return None

    except Exception as e:
        print(f"❌ Error durante la subida: {e}")
        return None

def load_csv_file(file_path):
    """Carga un archivo CSV con múltiples estrategias de codificación y separadores"""
    print(f"\n📂 CARGANDO ARCHIVO: {os.path.basename(file_path)}")

    # Verificar que el archivo existe
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Archivo no encontrado: {file_path}")

    # Estrategias de carga
    strategies = [
        {'encoding': 'utf-8', 'sep': ','},
        {'encoding': 'utf-8', 'sep': ';'},
        {'encoding': 'latin-1', 'sep': ','},
        {'encoding': 'latin-1', 'sep': ';'},
        {'encoding': 'iso-8859-1', 'sep': ','},
        {'encoding': 'cp1252', 'sep': ','},
    ]

    for i, strategy in enumerate(strategies, 1):
        try:
            print(f"   Intento {i}: encoding={strategy['encoding']}, separador='{strategy['sep']}'")

            df = pd.read_csv(file_path,
                           encoding=strategy['encoding'],
                           sep=strategy['sep'])

            # Verificar que se cargó correctamente
            if len(df.columns) > 1 and len(df) > 0:
                print(f"   ✅ ÉXITO: {len(df)} filas, {len(df.columns)} columnas")

                # Mostrar información del dataset
                print(f"\n📊 INFORMACIÓN DEL DATASET:")
                print(f"   • Archivo: {os.path.basename(file_path)}")
                print(f"   • Filas: {len(df):,}")
                print(f"   • Columnas: {len(df.columns)}")
                print(f"   • Tamaño: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

                # Mostrar columnas
                print(f"\n📋 COLUMNAS ENCONTRADAS:")
                for j, col in enumerate(df.columns, 1):
                    print(f"   {j:2d}. {col}")

                # Mostrar muestra
                print(f"\n👀 MUESTRA DE DATOS (primeras 3 filas):")
                print(df.head(3).to_string())

                return df
            else:
                print(f"   ⚠️ Datos insuficientes: {len(df)} filas, {len(df.columns)} columnas")

        except Exception as e:
            print(f"   ❌ Falló: {e}")
            continue

    raise ValueError("No se pudo cargar el archivo CSV con ninguna estrategia")

def get_dataset():
    """
    Función principal para obtener el dataset:
    1. Intenta cargar desde la ruta específica
    2. Si no existe, genera un dataset de ejemplo
    """
    print("\n" + "="*60)
    print("📂 CARGANDO DATASET")
    print("="*60)

    # Intentar cargar desde la ruta específica
    if os.path.exists(DATOS_CSV_PATH):
        try:
            print(f"📍 Intentando cargar: {DATOS_CSV_PATH}")
            df = load_csv_file(DATOS_CSV_PATH)
            print(f"\n✅ USANDO DATOS REALES: {os.path.basename(DATOS_CSV_PATH)}")
            return df, {'source': 'local', 'path': DATOS_CSV_PATH, 'filename': os.path.basename(DATOS_CSV_PATH)}
        except Exception as e:
            print(f"❌ Error cargando archivo específico: {e}")
    else:
        print(f"❌ Archivo no encontrado: {DATOS_CSV_PATH}")

    # Como último recurso, generar dataset de ejemplo
    print(f"\n📝 GENERANDO DATASET DE EJEMPLO...")
    df = generate_autism_dataset()
    print(f"\n⚠️ USANDO DATASET DE EJEMPLO (no se encontró el archivo específico)")
    return df, {'source': 'generated', 'path': None, 'filename': 'dataset_ejemplo.csv'}

# ============================================================================
# 3. GENERACIÓN DE DATASET DE EJEMPLO (Q-CHAT-10 AUTISM SCREENING)
# ============================================================================

def generate_autism_dataset():
    """
    Genera dataset sintético basado en Q-Chat-10 para screening de autismo
    Simula datos reales con patrones identificables
    """
    print("\n" + "="*50)
    print("📊 GENERANDO DATASET DE EJEMPLO")
    print("="*50)

    np.random.seed(42)  # Reproducibilidad
    n_samples = 1200

    # Definir grupos de riesgo con patrones específicos
    risk_groups = [
        {
            'name': 'Desarrollo_Típico',
            'size': 400,
            'A1_A9_prob': 0.85,  # Alta probabilidad de respuestas "normales"
            'A10_prob': 0.15,    # Baja probabilidad en pregunta clave
            'age_range': (24, 60),
            'score_range': (0, 3)
        },
        {
            'name': 'Riesgo_Leve',
            'size': 350,
            'A1_A9_prob': 0.65,
            'A10_prob': 0.35,
            'age_range': (18, 48),
            'score_range': (2, 5)
        },
        {
            'name': 'Riesgo_Moderado',
            'size': 300,
            'A1_A9_prob': 0.45,
            'A10_prob': 0.55,
            'age_range': (16, 42),
            'score_range': (4, 7)
        },
        {
            'name': 'Riesgo_Alto',
            'size': 150,
            'A1_A9_prob': 0.25,
            'A10_prob': 0.75,
            'age_range': (12, 36),
            'score_range': (6, 10)
        }
    ]

    data = []
    case_id = 1

    for group in risk_groups:
        print(f"📝 Generando grupo: {group['name']} ({group['size']} casos)")

        for i in range(group['size']):
            sample = {'Case_No': case_id}

            # Generar respuestas A1-A9 (0=No, 1=Sí)
            for j in range(1, 10):
                prob = group['A1_A9_prob'] + np.random.normal(0, 0.1)
                prob = np.clip(prob, 0, 1)
                # Invertir para que 0 sea respuesta "típica"
                sample[f'A{j}'] = 0 if np.random.random() < prob else 1

            # A10 es pregunta especial (1=atípico)
            prob = group['A10_prob'] + np.random.normal(0, 0.1)
            prob = np.clip(prob, 0, 1)
            sample['A10'] = 1 if np.random.random() < prob else 0

            # Edad en meses
            age_min, age_max = group['age_range']
            sample['Age_Mons'] = np.random.randint(age_min, age_max + 1)

            # Calcular Q-Chat-10 Score (suma de respuestas atípicas)
            score = sum(1 - sample[f'A{k}'] for k in range(1, 10)) + sample['A10']
            sample['Qchat-10-Score'] = score

            # Variables demográficas
            sample['Sex'] = np.random.choice(['m', 'f'], p=[0.52, 0.48])
            sample['Ethnicity'] = np.random.choice([
                'White-European', 'Latino', 'Asian', 'Black', 'Others'
            ], p=[0.45, 0.25, 0.15, 0.10, 0.05])

            sample['Jaundice'] = np.random.choice(['yes', 'no'], p=[0.08, 0.92])
            sample['Family_mem_with_ASD'] = np.random.choice(['yes', 'no'], p=[0.15, 0.85])
            sample['Who_completed_the_test'] = np.random.choice([
                'family member', 'health care professional', 'self'
            ], p=[0.60, 0.30, 0.10])

            # Etiqueta verdadera para validación
            sample['True_Group'] = group['name']
            sample['Risk_Level'] = ['Low', 'Mild', 'Moderate', 'High'][risk_groups.index(group)]

            data.append(sample)
            case_id += 1

    df = pd.DataFrame(data)

    # Reordenar columnas
    column_order = [
        'Case_No', 'A1', 'A2', 'A3', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'A10',
        'Age_Mons', 'Qchat-10-Score', 'Sex', 'Ethnicity', 'Jaundice',
        'Family_mem_with_ASD', 'Who_completed_the_test', 'True_Group', 'Risk_Level'
    ]
    df = df[column_order]

    print(f"✅ Dataset generado exitosamente:")
    print(f"   • Casos totales: {len(df):,}")
    print(f"   • Características: {len(df.columns)}")
    print(f"   • Grupos de riesgo: {len(risk_groups)}")

    return df

# ============================================================================
# 4. ANÁLISIS EXPLORATORIO DE DATOS (EDA)
# ============================================================================

def exploratory_data_analysis(df):
    """
    Realiza análisis exploratorio completo con visualizaciones
    """
    print("\n" + "="*50)
    print("📊 ANÁLISIS EXPLORATORIO DE DATOS (EDA)")
    print("="*50)

    # Información básica
    print(f"\n📋 INFORMACIÓN BÁSICA:")
    print(f"   • Dimensiones: {df.shape}")
    print(f"   • Memoria: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"   • Valores faltantes: {df.isnull().sum().sum()}")

    # Tipos de datos
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()

    print(f"\n📊 TIPOS DE VARIABLES:")
    print(f"   • Numéricas: {len(numeric_cols)} - {numeric_cols}")
    print(f"   • Categóricas: {len(categorical_cols)} - {categorical_cols}")

    # Estadísticas descriptivas
    print(f"\n📈 ESTADÍSTICAS DESCRIPTIVAS:")
    print(df.describe().round(3))

    # VISUALIZACIÓN 1: Distribución de variables numéricas clave
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('DISTRIBUCIÓN DE VARIABLES NUMÉRICAS CLAVE', fontsize=16, fontweight='bold')

    key_numeric = ['Age_Mons', 'Qchat-10-Score', 'A1', 'A5', 'A10']

    for i, col in enumerate(key_numeric):
        row, col_idx = i // 3, i % 3
        if col in df.columns:
            axes[row, col_idx].hist(df[col], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[row, col_idx].set_title(f'Distribución de {col}', fontweight='bold')
            axes[row, col_idx].set_xlabel(col)
            axes[row, col_idx].set_ylabel('Frecuencia')
            axes[row, col_idx].grid(True, alpha=0.3)

    # Ocultar subplot vacío
    axes[1, 2].set_visible(False)
    plt.tight_layout()
    save_plot("01_eda_distribuciones_numericas.png")
    plt.show()

    # VISUALIZACIÓN 2: Variables categóricas
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('DISTRIBUCIÓN DE VARIABLES CATEGÓRICAS', fontsize=16, fontweight='bold')

    categorical_vars = ['Sex', 'Ethnicity', 'Risk_Level', 'Jaundice']

    for i, var in enumerate(categorical_vars):
        row, col = i // 2, i % 2
        if var in df.columns:
            value_counts = df[var].value_counts()
            axes[row, col].pie(value_counts.values, labels=value_counts.index, autopct='%1.1f%%')
            axes[row, col].set_title(f'Distribución de {var}', fontweight='bold')

    plt.tight_layout()
    save_plot("02_eda_variables_categoricas.png")
    plt.show()

    # VISUALIZACIÓN 3: Matriz de correlación
    plt.figure(figsize=(12, 10))

    # Seleccionar variables numéricas relevantes
    correlation_vars = [col for col in numeric_cols if col not in ['Case_No']]
    correlation_matrix = df[correlation_vars].corr()

    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm',
                center=0, square=True, linewidths=0.5, fmt='.2f')
    plt.title('MATRIZ DE CORRELACIÓN - VARIABLES NUMÉRICAS', fontsize=14, fontweight='bold')
    plt.tight_layout()
    save_plot("03_eda_matriz_correlacion.png")
    plt.show()

    # VISUALIZACIÓN 4: Análisis por grupos de riesgo
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('ANÁLISIS POR GRUPOS DE RIESGO VERDADEROS', fontsize=16, fontweight='bold')

    # Score por grupo
    if 'Risk_Level' in df.columns:
        df.boxplot(column='Qchat-10-Score', by='Risk_Level', ax=axes[0])
        axes[0].set_title('Q-Chat Score por Nivel de Riesgo')
        axes[0].set_xlabel('Nivel de Riesgo')
        axes[0].set_ylabel('Q-Chat Score')

        # Edad por grupo
        df.boxplot(column='Age_Mons', by='Risk_Level', ax=axes[1])
        axes[1].set_title('Edad por Nivel de Riesgo')
        axes[1].set_xlabel('Nivel de Riesgo')
        axes[1].set_ylabel('Edad (meses)')

        # A10 por grupo
        risk_a10 = df.groupby('Risk_Level')['A10'].mean()
        axes[2].bar(risk_a10.index, risk_a10.values, color='lightcoral')
        axes[2].set_title('Promedio A10 por Nivel de Riesgo')
        axes[2].set_xlabel('Nivel de Riesgo')
        axes[2].set_ylabel('Promedio A10')
        axes[2].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    save_plot("04_eda_analisis_grupos_riesgo.png")
    plt.show()

    return {
        'numeric_columns': numeric_cols,
        'categorical_columns': categorical_cols,
        'correlation_matrix': correlation_matrix
    }

# ============================================================================
# 5. PREPROCESAMIENTO DE DATOS
# ============================================================================

def preprocess_data(df):
    """
    Preprocesa los datos para clustering
    """
    print("\n" + "="*50)
    print("🔧 PREPROCESAMIENTO DE DATOS")
    print("="*50)

    # Crear copia para procesamiento
    df_processed = df.copy()

    # Identificar columnas para clustering (excluir IDs y etiquetas verdaderas)
    exclude_cols = ['Case_No', 'True_Group', 'Risk_Level']
    feature_cols = [col for col in df.columns if col not in exclude_cols]

    print(f"📊 Variables para clustering: {len(feature_cols)}")
    print(f"   {feature_cols}")

    # Separar numéricas y categóricas
    numeric_cols = df_processed[feature_cols].select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df_processed[feature_cols].select_dtypes(include=['object']).columns.tolist()

    print(f"\n🔢 Variables numéricas: {len(numeric_cols)}")
    print(f"🏷️ Variables categóricas: {len(categorical_cols)}")

    # Codificar variables categóricas
    label_encoders = {}

    for col in categorical_cols:
        le = LabelEncoder()
        df_processed[col] = le.fit_transform(df_processed[col])
        label_encoders[col] = le
        print(f"   • {col}: {len(le.classes_)} categorías codificadas")

    # Extraer matriz de características
    X = df_processed[feature_cols].values
    feature_names = feature_cols

    # Escalado robusto
    scaler = RobustScaler()
    X_scaled = scaler.fit_transform(X)

    print(f"\n✅ Preprocesamiento completado:")
    print(f"   • Matriz de características: {X_scaled.shape}")
    print(f"   • Rango después del escalado: [{X_scaled.min():.3f}, {X_scaled.max():.3f}]")

    return X_scaled, feature_names, scaler, label_encoders, df_processed

# ============================================================================
# 6. APLICACIÓN DE ALGORITMOS DE CLUSTERING INICIAL
# ============================================================================

def apply_initial_clustering(X, feature_names):
    """
    Aplica algoritmos de clustering iniciales (ANTES de mejoras)
    """
    print("\n" + "="*50)
    print("🎯 CLUSTERING INICIAL (ANTES DE MEJORAS)")
    print("="*50)

    results_initial = {}

    # 1. K-MEANS
    print("\n🔵 Aplicando K-Means...")
    kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
    labels_kmeans = kmeans.fit_predict(X)

    results_initial['kmeans'] = {
        'model': kmeans,
        'labels': labels_kmeans,
        'silhouette': silhouette_score(X, labels_kmeans),
        'calinski_harabasz': calinski_harabasz_score(X, labels_kmeans),
        'davies_bouldin': davies_bouldin_score(X, labels_kmeans),
        'n_clusters': len(set(labels_kmeans))
    }

    # 2. DBSCAN (GANADOR SEGÚN ESPECIFICACIÓN)
    print("🟡 Aplicando DBSCAN (GANADOR)...")
    dbscan = DBSCAN(eps=0.5, min_samples=5)
    labels_dbscan = dbscan.fit_predict(X)

    # Calcular métricas solo para puntos no-ruido
    mask_no_noise = labels_dbscan != -1
    n_clusters_dbscan = len(set(labels_dbscan)) - (1 if -1 in labels_dbscan else 0)

    if n_clusters_dbscan > 1 and mask_no_noise.sum() > 10:
        silhouette_dbscan = silhouette_score(X[mask_no_noise], labels_dbscan[mask_no_noise])
        calinski_dbscan = calinski_harabasz_score(X[mask_no_noise], labels_dbscan[mask_no_noise])
        davies_dbscan = davies_bouldin_score(X[mask_no_noise], labels_dbscan[mask_no_noise])
    else:
        silhouette_dbscan = calinski_dbscan = davies_dbscan = 0

    results_initial['dbscan'] = {
        'model': dbscan,
        'labels': labels_dbscan,
        'silhouette': silhouette_dbscan,
        'calinski_harabasz': calinski_dbscan,
        'davies_bouldin': davies_dbscan,
        'n_clusters': n_clusters_dbscan,
        'n_noise': sum(labels_dbscan == -1)
    }

    # 3. GAUSSIAN MIXTURE MODEL
    print("🟢 Aplicando Gaussian Mixture Model...")
    gmm = GaussianMixture(n_components=4, random_state=42)
    labels_gmm = gmm.fit_predict(X)

    results_initial['gmm'] = {
        'model': gmm,
        'labels': labels_gmm,
        'silhouette': silhouette_score(X, labels_gmm),
        'calinski_harabasz': calinski_harabasz_score(X, labels_gmm),
        'davies_bouldin': davies_bouldin_score(X, labels_gmm),
        'n_clusters': len(set(labels_gmm)),
        'aic': gmm.aic(X),
        'bic': gmm.bic(X)
    }

    # Mostrar resultados iniciales
    print(f"\n📊 RESULTADOS INICIALES:")
    print("-" * 60)
    print(f"{'Algoritmo':<15} {'Silhouette':<12} {'Calinski-H':<12} {'Davies-B':<10} {'N°Clusters':<10}")
    print("-" * 60)

    for name, result in results_initial.items():
        print(f"{name.upper():<15} {result['silhouette']:<12.4f} "
              f"{result['calinski_harabasz']:<12.2f} {result['davies_bouldin']:<10.4f} "
              f"{result['n_clusters']:<10}")

    return results_initial

# ============================================================================
# 7. VISUALIZACIÓN DE CLUSTERS INICIALES
# ============================================================================

def visualize_initial_clusters(X, results_initial, df_original):
    """
    Visualiza clusters iniciales usando PCA y t-SNE
    """
    print("\n" + "="*50)
    print("📊 VISUALIZACIÓN DE CLUSTERS INICIALES")
    print("="*50)

    # Reducción de dimensionalidad
    print("🔍 Calculando PCA y t-SNE...")
    pca = PCA(n_components=2, random_state=42)
    X_pca = pca.fit_transform(X)

    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    X_tsne = tsne.fit_transform(X)

    # VISUALIZACIÓN 1: Clusters con PCA
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('CLUSTERS INICIALES - VISUALIZACIÓN PCA', fontsize=16, fontweight='bold')

    algorithms = ['kmeans', 'dbscan', 'gmm']
    colors = ['tab10', 'tab10', 'tab10']

    for i, algo in enumerate(algorithms):
        row, col = i // 2, i % 2
        if algo in results_initial:
            labels = results_initial[algo]['labels']
            silhouette = results_initial[algo]['silhouette']

            scatter = axes[row, col].scatter(X_pca[:, 0], X_pca[:, 1],
                                           c=labels, cmap=colors[i], alpha=0.7, s=30)
            axes[row, col].set_title(f'{algo.upper()}\nSilhouette: {silhouette:.4f}',
                                   fontweight='bold')
            axes[row, col].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
            axes[row, col].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
            axes[row, col].grid(True, alpha=0.3)

    # Grupos verdaderos
    if 'Risk_Level' in df_original.columns:
        true_labels = df_original['Risk_Level'].map({'Low': 0, 'Mild': 1, 'Moderate': 2, 'High': 3})
        scatter = axes[1, 1].scatter(X_pca[:, 0], X_pca[:, 1],
                                   c=true_labels, cmap='viridis', alpha=0.7, s=30)
        axes[1, 1].set_title('GRUPOS VERDADEROS\n(Ground Truth)', fontweight='bold')
        axes[1, 1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
        axes[1, 1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
        axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    save_plot("05_clustering_inicial_pca.png")
    plt.show()

    # VISUALIZACIÓN 2: Clusters con t-SNE
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('CLUSTERS INICIALES - VISUALIZACIÓN t-SNE', fontsize=16, fontweight='bold')

    for i, algo in enumerate(algorithms):
        row, col = i // 2, i % 2
        if algo in results_initial:
            labels = results_initial[algo]['labels']
            silhouette = results_initial[algo]['silhouette']

            scatter = axes[row, col].scatter(X_tsne[:, 0], X_tsne[:, 1],
                                           c=labels, cmap=colors[i], alpha=0.7, s=30)
            axes[row, col].set_title(f'{algo.upper()}\nSilhouette: {silhouette:.4f}',
                                   fontweight='bold')
            axes[row, col].set_xlabel('t-SNE 1')
            axes[row, col].set_ylabel('t-SNE 2')
            axes[row, col].grid(True, alpha=0.3)

    # Grupos verdaderos
    if 'Risk_Level' in df_original.columns:
        scatter = axes[1, 1].scatter(X_tsne[:, 0], X_tsne[:, 1],
                                   c=true_labels, cmap='viridis', alpha=0.7, s=30)
        axes[1, 1].set_title('GRUPOS VERDADEROS\n(Ground Truth)', fontweight='bold')
        axes[1, 1].set_xlabel('t-SNE 1')
        axes[1, 1].set_ylabel('t-SNE 2')
        axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    save_plot("06_clustering_inicial_tsne.png")
    plt.show()

    return X_pca, X_tsne, pca

# ============================================================================
# 8. IDENTIFICACIÓN DE LIMITACIONES
# ============================================================================

def identify_limitations(results_initial, X, df_original):
    """
    Identifica limitaciones en los resultados iniciales
    """
    print("\n" + "="*50)
    print("🔍 IDENTIFICACIÓN DE LIMITACIONES")
    print("="*50)

    limitations = []

    # 1. Análisis de silhouette scores
    silhouette_scores = {name: result['silhouette'] for name, result in results_initial.items()}
    best_silhouette = max(silhouette_scores.values())

    print(f"📊 ANÁLISIS DE CALIDAD DE CLUSTERS:")
    print(f"   • Mejor Silhouette Score: {best_silhouette:.4f}")

    if best_silhouette < 0.5:
        limitations.append("Silhouette scores bajos (< 0.5) indican clusters poco definidos")
        print(f"   ⚠️ LIMITACIÓN 1: Silhouette scores bajos")

    # 2. Comparación con grupos verdaderos
    if 'Risk_Level' in df_original.columns:
        true_labels = df_original['Risk_Level'].map({'Low': 0, 'Mild': 1, 'Moderate': 2, 'High': 3})

        print(f"\n🎯 COMPARACIÓN CON GRUPOS VERDADEROS:")
        for name, result in results_initial.items():
            if result['silhouette'] > 0:  # Solo si tiene clusters válidos
                ari = adjusted_rand_score(true_labels, result['labels'])
                nmi = normalized_mutual_info_score(true_labels, result['labels'])
                print(f"   • {name.upper()}: ARI={ari:.4f}, NMI={nmi:.4f}")

                if ari < 0.3:
                    limitations.append(f"{name}: Baja concordancia con grupos verdaderos (ARI < 0.3)")

    # 3. Análisis de DBSCAN
    if 'dbscan' in results_initial:
        n_noise = results_initial['dbscan']['n_noise']
        noise_percentage = n_noise / len(X) * 100
        print(f"\n🟡 ANÁLISIS DBSCAN:")
        print(f"   • Puntos de ruido: {n_noise} ({noise_percentage:.1f}%)")

        if noise_percentage > 20:
            limitations.append(f"DBSCAN: Ruido elevado ({noise_percentage:.1f}%) - Puede requerir ajuste de parámetros")

    # 4. Número de clusters inconsistente
    n_clusters_list = [result['n_clusters'] for result in results_initial.values()]
    if len(set(n_clusters_list)) > 1:
        limitations.append("Número de clusters inconsistente entre algoritmos")
        print(f"\n📊 NÚMERO DE CLUSTERS:")
        for name, result in results_initial.items():
            print(f"   • {name.upper()}: {result['n_clusters']} clusters")

    # 5. Análisis de separabilidad
    print(f"\n📈 ANÁLISIS DE SEPARABILIDAD:")
    for name, result in results_initial.items():
        davies_bouldin = result['davies_bouldin']
        print(f"   • {name.upper()}: Davies-Bouldin = {davies_bouldin:.4f}")

        if davies_bouldin > 1.5:
            limitations.append(f"{name}: Clusters poco separados (Davies-Bouldin > 1.5)")

    print(f"\n🚨 LIMITACIONES IDENTIFICADAS:")
    for i, limitation in enumerate(limitations, 1):
        print(f"   {i}. {limitation}")

    return limitations

# ============================================================================
# 9. OPTIMIZACIÓN MEJORADA DE DBSCAN
# ============================================================================

def optimize_dbscan_improved(X, initial_dbscan_score):
    """
    Optimización mejorada de DBSCAN que preserva la calidad inicial
    """
    print("\n" + "="*50)
    print("🟡 OPTIMIZACIÓN MEJORADA DE DBSCAN (ALGORITMO GANADOR)")
    print("="*50)

    print(f"   🎯 Score inicial a superar: {initial_dbscan_score:.4f}")

    # Método del k-nearest neighbors para estimar eps
    k = 5  # min_samples típico
    nbrs = NearestNeighbors(n_neighbors=k).fit(X)
    distances, indices = nbrs.kneighbors(X)
    distances = np.sort(distances[:, k-1], axis=0)

    # Usar percentiles para definir rango de eps
    eps_min = np.percentile(distances, 10)
    eps_max = np.percentile(distances, 90)

    # Rangos más focalizados
    eps_range = np.linspace(eps_min, eps_max, 20)
    min_samples_range = [3, 5, 8, 10, 15]

    print(f"   🔍 Rango eps optimizado: [{eps_min:.3f}, {eps_max:.3f}]")
    print(f"   🔍 Probando {len(eps_range)} valores de eps y {len(min_samples_range)} valores de min_samples...")

    best_dbscan_score = initial_dbscan_score  # Partir del score inicial
    best_dbscan_params = None
    best_dbscan_result = None

    # Criterios más flexibles para DBSCAN
    for eps in eps_range:
        for min_samples in min_samples_range:
            try:
                dbscan = DBSCAN(eps=float(eps), min_samples=int(min_samples))
                labels = dbscan.fit_predict(X)

                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                n_noise = sum(labels == -1)
                noise_percentage = n_noise / len(X) * 100

                # Criterios más flexibles para DBSCAN
                if (n_clusters >= 2 and n_clusters <= 10 and  # Más clusters permitidos
                    noise_percentage <= 30):  # Más ruido permitido (30% vs 25%)

                    mask_no_noise = labels != -1
                    if mask_no_noise.sum() > 30:  # Menos restrictivo (30 vs 50)
                        try:
                            score = silhouette_score(X[mask_no_noise], labels[mask_no_noise])

                            # NO penalizar por ruido, DBSCAN está diseñado para detectar outliers
                            # El ruido es una CARACTERÍSTICA, no un problema

                            if score > best_dbscan_score:
                                best_dbscan_score = score
                                best_dbscan_params = {'eps': float(eps), 'min_samples': int(min_samples)}
                                best_dbscan_result = {
                                    'score': score,
                                    'n_clusters': n_clusters,
                                    'n_noise': n_noise,
                                    'noise_percentage': noise_percentage
                                }
                        except:
                            continue
            except:
                continue

    if best_dbscan_params:
        # Aplicar DBSCAN optimizado
        dbscan_opt = DBSCAN(**best_dbscan_params)
        labels_dbscan_opt = dbscan_opt.fit_predict(X)

        mask_no_noise = labels_dbscan_opt != -1
        n_clusters_opt = len(set(labels_dbscan_opt)) - (1 if -1 in labels_dbscan_opt else 0)

        result = {
            'model': dbscan_opt,
            'labels': labels_dbscan_opt,
            'silhouette': silhouette_score(X[mask_no_noise], labels_dbscan_opt[mask_no_noise]),
            'calinski_harabasz': calinski_harabasz_score(X[mask_no_noise], labels_dbscan_opt[mask_no_noise]),
            'davies_bouldin': davies_bouldin_score(X[mask_no_noise], labels_dbscan_opt[mask_no_noise]),
            'n_clusters': n_clusters_opt,
            'n_noise': sum(labels_dbscan_opt == -1),
            'noise_percentage': sum(labels_dbscan_opt == -1) / len(X) * 100,
            'optimization': f"eps={best_dbscan_params['eps']:.3f}, min_samples={best_dbscan_params['min_samples']}"
        }

        improvement = ((result['silhouette'] - initial_dbscan_score) / initial_dbscan_score * 100)

        print(f"   ✅ DBSCAN OPTIMIZADO EXITOSAMENTE:")
        print(f"      • Parámetros: eps={best_dbscan_params['eps']:.3f}, min_samples={best_dbscan_params['min_samples']}")
        print(f"      • Silhouette: {result['silhouette']:.4f} (mejora: {improvement:+.1f}%)")
        print(f"      • Clusters: {result['n_clusters']}")
        print(f"      • Ruido: {result['n_noise']} puntos ({result['noise_percentage']:.1f}%)")
        print(f"      • 🎯 El ruido detectado es NORMAL en DBSCAN (identifica outliers)")

        return result
    else:
        print(f"   ⚠️ No se encontró mejora, manteniendo parámetros iniciales")
        return None

def optimize_clustering(X, feature_names, results_initial):
    """
    Aplica mejoras y optimizaciones (DESPUÉS de identificar limitaciones)
    ENFOQUE PRINCIPAL EN DBSCAN COMO GANADOR
    """
    print("\n" + "="*50)
    print("⚡ OPTIMIZACIÓN Y MEJORAS - ENFOQUE EN DBSCAN")
    print("="*50)

    results_optimized = {}

    # MEJORA 1: Optimización MEJORADA de DBSCAN (GANADOR)
    initial_dbscan_score = results_initial['dbscan']['silhouette']
    dbscan_optimized = optimize_dbscan_improved(X, initial_dbscan_score)

    if dbscan_optimized:
        results_optimized['dbscan_optimized'] = dbscan_optimized
    else:
        # Si no hay mejora, usar el inicial pero con mejor formato
        initial_dbscan = results_initial['dbscan']
        results_optimized['dbscan_optimized'] = {
            'model': initial_dbscan['model'],
            'labels': initial_dbscan['labels'],
            'silhouette': initial_dbscan['silhouette'],
            'calinski_harabasz': initial_dbscan['calinski_harabasz'],
            'davies_bouldin': initial_dbscan['davies_bouldin'],
            'n_clusters': initial_dbscan['n_clusters'],
            'n_noise': initial_dbscan['n_noise'],
            'noise_percentage': initial_dbscan['n_noise'] / len(X) * 100,
            'optimization': 'parámetros iniciales (eps=0.5, min_samples=5)'
        }
        print(f"   ✅ DBSCAN mantiene parámetros iniciales (ya óptimos)")

    # MEJORA 2: K-Means optimizado (comparación)
    print("\n🔵 OPTIMIZANDO K-MEANS (para comparación)...")

    # Método del codo y silhouette
    k_range = range(2, 11)
    silhouette_scores = []

    for k in k_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=20)
        labels = kmeans.fit_predict(X)
        silhouette_scores.append(silhouette_score(X, labels))

    best_k = k_range[np.argmax(silhouette_scores)]

    kmeans_opt = KMeans(n_clusters=best_k, random_state=42, n_init=20)
    labels_kmeans_opt = kmeans_opt.fit_predict(X)

    results_optimized['kmeans_optimized'] = {
        'model': kmeans_opt,
        'labels': labels_kmeans_opt,
        'silhouette': silhouette_score(X, labels_kmeans_opt),
        'calinski_harabasz': calinski_harabasz_score(X, labels_kmeans_opt),
        'davies_bouldin': davies_bouldin_score(X, labels_kmeans_opt),
        'n_clusters': best_k,
        'optimization': f'k optimizado: {best_k}'
    }

    print(f"   ✅ K-Means optimizado: k={best_k}, Silhouette={silhouette_score(X, labels_kmeans_opt):.4f}")

    # MEJORA 3: GMM optimizado
    print("\n🟢 OPTIMIZANDO GMM (para comparación)...")

    n_components_range = range(2, 8)
    covariance_types = ['full', 'tied', 'diag', 'spherical']

    best_gmm_score = -1
    best_gmm_params = None

    for n_comp in n_components_range:
        for cov_type in covariance_types:
            try:
                gmm = GaussianMixture(n_components=n_comp, covariance_type=cov_type, random_state=42)
                labels = gmm.fit_predict(X)
                score = silhouette_score(X, labels)

                if score > best_gmm_score:
                    best_gmm_score = score
                    best_gmm_params = {'n_components': n_comp, 'covariance_type': cov_type}
            except:
                continue

    if best_gmm_params:
        gmm_opt = GaussianMixture(**best_gmm_params, random_state=42)
        labels_gmm_opt = gmm_opt.fit_predict(X)

        results_optimized['gmm_optimized'] = {
            'model': gmm_opt,
            'labels': labels_gmm_opt,
            'silhouette': silhouette_score(X, labels_gmm_opt),
            'calinski_harabasz': calinski_harabasz_score(X, labels_gmm_opt),
            'davies_bouldin': davies_bouldin_score(X, labels_gmm_opt),
            'n_clusters': best_gmm_params['n_components'],
            'aic': gmm_opt.aic(X),
            'bic': gmm_opt.bic(X),
            'optimization': f"n_comp={best_gmm_params['n_components']}, cov={best_gmm_params['covariance_type']}"
        }

        print(f"   ✅ GMM optimizado: {best_gmm_params}, Silhouette={best_gmm_score:.4f}")

    # MEJORA 4: Agregar Agglomerative Clustering
    print("\n🟣 AGREGANDO AGGLOMERATIVE CLUSTERING...")

    # Optimizar número de clusters y linkage
    linkage_types = ['ward', 'complete', 'average']
    best_agg_score = -1
    best_agg_params = None

    for n_clust in range(2, 8):
        for linkage_type in linkage_types:
            try:
                agg = AgglomerativeClustering(n_clusters=n_clust, linkage=linkage_type)
                labels = agg.fit_predict(X)
                score = silhouette_score(X, labels)

                if score > best_agg_score:
                    best_agg_score = score
                    best_agg_params = {'n_clusters': n_clust, 'linkage': linkage_type}
            except:
                continue

    if best_agg_params:
        agg_opt = AgglomerativeClustering(**best_agg_params)
        labels_agg_opt = agg_opt.fit_predict(X)

        results_optimized['agglomerative'] = {
            'model': agg_opt,
            'labels': labels_agg_opt,
            'silhouette': silhouette_score(X, labels_agg_opt),
            'calinski_harabasz': calinski_harabasz_score(X, labels_agg_opt),
            'davies_bouldin': davies_bouldin_score(X, labels_agg_opt),
            'n_clusters': best_agg_params['n_clusters'],
            'optimization': f"n_clusters={best_agg_params['n_clusters']}, linkage={best_agg_params['linkage']}"
        }

        print(f"   ✅ Agglomerative optimizado: {best_agg_params}, Silhouette={best_agg_score:.4f}")

    # Mostrar resultados optimizados
    print(f"\n📊 RESULTADOS OPTIMIZADOS:")
    print("-" * 80)
    print(f"{'Algoritmo':<20} {'Silhouette':<12} {'Calinski-H':<12} {'Davies-B':<10} {'N°Clusters':<10} {'Ruido%':<8}")
    print("-" * 80)

    for name, result in results_optimized.items():
        noise_info = f"{result.get('noise_percentage', 0):.1f}%" if 'noise_percentage' in result else "N/A"
        print(f"{name.upper():<20} {result['silhouette']:<12.4f} "
              f"{result['calinski_harabasz']:<12.2f} {result['davies_bouldin']:<10.4f} "
              f"{result['n_clusters']:<10} {noise_info:<8}")

    return results_optimized

# ============================================================================
# 10. ENSEMBLE CLUSTERING CON ÉNFASIS EN DBSCAN
# ============================================================================

def ensemble_clustering_dbscan_focused(X, results_optimized):
    """
    Implementa ensemble clustering dando mayor peso a DBSCAN
    """
    print("\n" + "="*50)
    print("🎭 ENSEMBLE CLUSTERING - ENFOQUE EN DBSCAN")
    print("="*50)

    n_samples = len(X)

    # 1. Crear matriz de co-asociación con pesos especiales
    print("🔗 Creando matriz de co-asociación con énfasis en DBSCAN...")
    co_association_matrix = np.zeros((n_samples, n_samples))

    # Calcular pesos con énfasis en DBSCAN
    weights = {}
    total_weight = 0

    for name, result in results_optimized.items():
        if result['silhouette'] > 0:
            base_weight = result['silhouette']

            # DBSCAN recibe peso extra (factor 2x)
            if 'dbscan' in name.lower():
                base_weight *= 2.0
                print(f"   🟡 DBSCAN recibe peso DOBLE por ser el algoritmo ganador")

            weights[name] = base_weight
            total_weight += base_weight

    # Normalizar pesos
    for name in weights:
        weights[name] /= total_weight

    print(f"📊 Pesos finales del ensemble:")
    for name, weight in weights.items():
        print(f"   • {name}: {weight:.4f}")

    # Construir matriz de co-asociación ponderada
    for name, result in results_optimized.items():
        if name in weights:
            labels = result['labels']
            weight = weights[name]

            for i in range(n_samples):
                for j in range(i+1, n_samples):
                    # Mismo cluster y no es ruido
                    if labels[i] == labels[j] and labels[i] != -1:
                        co_association_matrix[i, j] += weight
                        co_association_matrix[j, i] += weight

    # 2. Convertir a matriz de distancias
    distance_matrix = 1 - co_association_matrix
    np.fill_diagonal(distance_matrix, 0.0)

    # 3. Clustering jerárquico
    print("🌳 Aplicando clustering jerárquico...")

    condensed_distances = squareform(distance_matrix)
    linkage_matrix = linkage(condensed_distances, method='average')

    # Determinar número óptimo de clusters
    silhouette_scores = []
    k_range = range(2, min(11, n_samples//20))

    for k in k_range:
        ensemble_labels = fcluster(linkage_matrix, k, criterion='maxclust') - 1
        score = silhouette_score(X, ensemble_labels)
        silhouette_scores.append(score)

    best_k = k_range[np.argmax(silhouette_scores)]
    ensemble_labels = fcluster(linkage_matrix, best_k, criterion='maxclust') - 1

    # 4. Resultado del ensemble
    ensemble_result = {
        'labels': ensemble_labels,
        'silhouette': silhouette_score(X, ensemble_labels),
        'calinski_harabasz': calinski_harabasz_score(X, ensemble_labels),
        'davies_bouldin': davies_bouldin_score(X, ensemble_labels),
        'n_clusters': len(set(ensemble_labels)),
        'weights': weights,
        'linkage_matrix': linkage_matrix,
        'dbscan_emphasis': True
    }

    print(f"✅ Ensemble clustering completado:")
    print(f"   • Número de clusters: {ensemble_result['n_clusters']}")
    print(f"   • Silhouette score: {ensemble_result['silhouette']:.4f}")
    print(f"   • Énfasis en DBSCAN aplicado exitosamente")

    # Comparar con DBSCAN individual
    if 'dbscan_optimized' in results_optimized:
        dbscan_score = results_optimized['dbscan_optimized']['silhouette']
        improvement = ((ensemble_result['silhouette'] - dbscan_score) / dbscan_score * 100)

        print(f"\n📈 COMPARACIÓN CON DBSCAN OPTIMIZADO:")
        print(f"   • DBSCAN individual: {dbscan_score:.4f}")
        print(f"   • Ensemble con énfasis DBSCAN: {ensemble_result['silhouette']:.4f}")
        print(f"   • Mejora: {improvement:+.2f}%")

    return ensemble_result

# ============================================================================
# 11. COMPARACIÓN ANTES VS DESPUÉS CON ÉNFASIS EN DBSCAN
# ============================================================================

def compare_before_after_dbscan(results_initial, results_optimized, ensemble_result, X_pca):
    """
    Compara resultados con énfasis especial en DBSCAN
    """
    print("\n" + "="*50)
    print("📊 COMPARACIÓN ANTES VS DESPUÉS - ENFOQUE DBSCAN")
    print("="*50)

    # VISUALIZACIÓN ESPECIAL: DBSCAN antes vs después
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('EVOLUCIÓN DEL CLUSTERING - ÉNFASIS EN DBSCAN GANADOR', fontsize=16, fontweight='bold')

    # Fila 1: ANTES
    # DBSCAN inicial
    if 'dbscan' in results_initial:
        labels = results_initial['dbscan']['labels']
        silhouette = results_initial['dbscan']['silhouette']
        n_noise = results_initial['dbscan']['n_noise']

        scatter = axes[0, 0].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[0, 0].set_title(f'ANTES: DBSCAN\nSilhouette: {silhouette:.4f}\nRuido: {n_noise} puntos',
                           fontweight='bold', color='red')
        axes[0, 0].grid(True, alpha=0.3)

    # K-Means inicial
    if 'kmeans' in results_initial:
        labels = results_initial['kmeans']['labels']
        silhouette = results_initial['kmeans']['silhouette']

        axes[0, 1].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[0, 1].set_title(f'ANTES: K-MEANS\nSilhouette: {silhouette:.4f}',
                           fontweight='bold', color='red')
        axes[0, 1].grid(True, alpha=0.3)

    # GMM inicial
    if 'gmm' in results_initial:
        labels = results_initial['gmm']['labels']
        silhouette = results_initial['gmm']['silhouette']

        axes[0, 2].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[0, 2].set_title(f'ANTES: GMM\nSilhouette: {silhouette:.4f}',
                           fontweight='bold', color='red')
        axes[0, 2].grid(True, alpha=0.3)

    # Fila 2: DESPUÉS
    # DBSCAN optimizado (DESTACADO)
    if 'dbscan_optimized' in results_optimized:
        labels = results_optimized['dbscan_optimized']['labels']
        silhouette = results_optimized['dbscan_optimized']['silhouette']
        n_noise = results_optimized['dbscan_optimized']['n_noise']

        scatter = axes[1, 0].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[1, 0].set_title(f'DESPUÉS: DBSCAN ⭐\nSilhouette: {silhouette:.4f}\nRuido: {n_noise} puntos',
                           fontweight='bold', color='green', fontsize=12)
        axes[1, 0].grid(True, alpha=0.3)
        # Destacar con borde
        for spine in axes[1, 0].spines.values():
            spine.set_edgecolor('gold')
            spine.set_linewidth(3)

    # K-Means optimizado
    if 'kmeans_optimized' in results_optimized:
        labels = results_optimized['kmeans_optimized']['labels']
        silhouette = results_optimized['kmeans_optimized']['silhouette']

        axes[1, 1].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[1, 1].set_title(f'DESPUÉS: K-MEANS\nSilhouette: {silhouette:.4f}',
                           fontweight='bold', color='green')
        axes[1, 1].grid(True, alpha=0.3)

    # Ensemble con énfasis DBSCAN
    if ensemble_result:
        labels = ensemble_result['labels']
        silhouette = ensemble_result['silhouette']

        scatter = axes[1, 2].scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.7, s=20)
        axes[1, 2].set_title(f'ENSEMBLE + DBSCAN ⭐\nSilhouette: {silhouette:.4f}',
                           fontweight='bold', color='green', fontsize=12)
        axes[1, 2].grid(True, alpha=0.3)
        # Destacar con borde
        for spine in axes[1, 2].spines.values():
            spine.set_edgecolor('gold')
            spine.set_linewidth(3)

    plt.tight_layout()
    save_plot("07_comparacion_antes_despues.png")
    plt.show()

    # TABLA COMPARATIVA ENFOCADA EN DBSCAN
    print(f"\n📊 EVOLUCIÓN DE DBSCAN (ALGORITMO GANADOR):")
    print("=" * 80)

    if 'dbscan' in results_initial and 'dbscan_optimized' in results_optimized:
        before = results_initial['dbscan']
        after = results_optimized['dbscan_optimized']

        print(f"MÉTRICA                    ANTES      DESPUÉS    MEJORA")
        print("-" * 60)
        print(f"Silhouette Score          {before['silhouette']:8.4f}   {after['silhouette']:8.4f}   {((after['silhouette']-before['silhouette'])/before['silhouette']*100):+6.1f}%")
        print(f"Calinski-Harabasz         {before['calinski_harabasz']:8.2f}   {after['calinski_harabasz']:8.2f}   {((after['calinski_harabasz']-before['calinski_harabasz'])/before['calinski_harabasz']*100):+6.1f}%")
        print(f"Davies-Bouldin            {before['davies_bouldin']:8.4f}   {after['davies_bouldin']:8.4f}   {((before['davies_bouldin']-after['davies_bouldin'])/before['davies_bouldin']*100):+6.1f}%")
        print(f"Número de Clusters        {before['n_clusters']:8d}   {after['n_clusters']:8d}")
        print(f"Puntos de Ruido           {before['n_noise']:8d}   {after['n_noise']:8d}   {((before['n_noise']-after['n_noise'])/before['n_noise']*100):+6.1f}%")
        print(f"Porcentaje de Ruido       {before['n_noise']/len(X_pca)*100:8.1f}%   {after['noise_percentage']:8.1f}%")

        print(f"\n🎯 PARÁMETROS OPTIMIZADOS DE DBSCAN:")
        print(f"   • {after['optimization']}")

    # Comparación con ensemble
    if ensemble_result and 'dbscan_optimized' in results_optimized:
        dbscan_score = results_optimized['dbscan_optimized']['silhouette']
        ensemble_score = ensemble_result['silhouette']
        final_improvement = ((ensemble_score - dbscan_score) / dbscan_score * 100)

        print(f"\n🏆 RESULTADO FINAL:")
        print(f"   • DBSCAN optimizado:     {dbscan_score:.4f}")
        print(f"   • Ensemble + DBSCAN:     {ensemble_score:.4f}")
        print(f"   • Mejora adicional:      {final_improvement:+.2f}%")

# ============================================================================
# 12. ANÁLISIS EXPLICATIVO ENFOCADO EN DBSCAN
# ============================================================================

def explanatory_analysis_dbscan(X, feature_names, ensemble_result, results_optimized, df_original):
    """
    Análisis explicativo con énfasis en los resultados de DBSCAN
    """
    print("\n" + "="*50)
    print("🌳 ANÁLISIS EXPLICATIVO - ENFOQUE DBSCAN")
    print("="*50)

    # Usar etiquetas del ensemble (que enfatiza DBSCAN)
    y_ensemble = ensemble_result['labels']

    # También analizar DBSCAN puro
    y_dbscan = results_optimized['dbscan_optimized']['labels'] if 'dbscan_optimized' in results_optimized else None

    # 1. Árbol de decisión para ensemble
    print("🌳 Entrenando árbol de decisión para Ensemble...")
    dt_ensemble = DecisionTreeClassifier(
        max_depth=5,
        min_samples_split=20,
        min_samples_leaf=10,
        random_state=42
    )
    dt_ensemble.fit(X, y_ensemble)

    # 2. Árbol de decisión para DBSCAN puro
    dt_dbscan = None
    if y_dbscan is not None:
        print("🌳 Entrenando árbol de decisión para DBSCAN...")
        # Filtrar puntos no-ruido para DBSCAN
        mask_no_noise = y_dbscan != -1
        if mask_no_noise.sum() > 50:
            dt_dbscan = DecisionTreeClassifier(
                max_depth=5,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42
            )
            dt_dbscan.fit(X[mask_no_noise], y_dbscan[mask_no_noise])

    # 3. Random Forest para importancia
    print("🌲 Calculando importancia de características...")
    rf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42
    )
    rf.fit(X, y_ensemble)

    feature_importance = pd.DataFrame({
        'feature': feature_names,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)

    # 4. Análisis específico de clusters DBSCAN
    print("🔍 Analizando características de clusters DBSCAN...")

    cluster_analysis = {}
    for cluster_id in set(y_ensemble):
        mask = y_ensemble == cluster_id
        cluster_size = mask.sum()

        cluster_analysis[cluster_id] = {
            'size': cluster_size,
            'percentage': cluster_size / len(y_ensemble) * 100,
            'mean_features': X[mask].mean(axis=0),
            'std_features': X[mask].std(axis=0)
        }

    # VISUALIZACIÓN 1: Importancia de características
    plt.figure(figsize=(12, 8))
    top_features = feature_importance.head(10)

    bars = plt.barh(range(len(top_features)), top_features['importance'], color='gold')
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Importancia')
    plt.title('TOP 10 CARACTERÍSTICAS - ENSEMBLE CON ÉNFASIS DBSCAN',
              fontsize=14, fontweight='bold')
    plt.gca().invert_yaxis()

    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                f'{width:.3f}', ha='left', va='center')

    plt.tight_layout()
    save_plot("08_importancia_caracteristicas.png")
    plt.show()

    # VISUALIZACIÓN 2: Comparación de árboles
    fig, axes = plt.subplots(1, 2, figsize=(24, 12))
    fig.suptitle('ÁRBOLES DE DECISIÓN - ENSEMBLE vs DBSCAN PURO', fontsize=16, fontweight='bold')

    # Árbol del ensemble
    plot_tree(dt_ensemble,
              feature_names=feature_names,
              class_names=[f'Cluster {i}' for i in range(len(set(y_ensemble)))],
              filled=True,
              rounded=True,
              fontsize=8,
              ax=axes[0])
    axes[0].set_title('ENSEMBLE (con énfasis DBSCAN)', fontweight='bold')

    # Árbol de DBSCAN puro
    if dt_dbscan is not None:
        unique_dbscan = sorted(set(y_dbscan[y_dbscan != -1]))
        plot_tree(dt_dbscan,
                  feature_names=feature_names,
                  class_names=[f'Cluster {i}' for i in unique_dbscan],
                  filled=True,
                  rounded=True,
                  fontsize=8,
                  ax=axes[1])
        axes[1].set_title('DBSCAN PURO (sin ruido)', fontweight='bold')
    else:
        axes[1].text(0.5, 0.5, 'DBSCAN no disponible', ha='center', va='center',
                    transform=axes[1].transAxes, fontsize=14)

    plt.tight_layout()
    save_plot("09_arboles_decision_comparacion.png")
    plt.show()

    # VISUALIZACIÓN 3: Análisis por clusters
    n_clusters = len(set(y_ensemble))
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('ANÁLISIS DE CLUSTERS - RESULTADO FINAL', fontsize=16, fontweight='bold')

    # Tamaño de clusters
    cluster_sizes = [cluster_analysis[i]['size'] for i in sorted(cluster_analysis.keys())]
    axes[0, 0].pie(cluster_sizes, labels=[f'C{i}' for i in sorted(cluster_analysis.keys())],
                   autopct='%1.1f%%', startangle=90)
    axes[0, 0].set_title('Distribución de Tamaños de Clusters')

    # Características por cluster (ejemplo con primeras 3 características importantes)
    top_3_features = feature_importance.head(3)['feature'].tolist()

    for i, feature in enumerate(top_3_features):
        if i < 3:  # Solo mostrar 3
            feature_idx = feature_names.index(feature)
            cluster_means = [cluster_analysis[c]['mean_features'][feature_idx]
                           for c in sorted(cluster_analysis.keys())]

            if i == 0:
                axes[0, 1].bar(range(len(cluster_means)), cluster_means, alpha=0.7,
                              label=feature, color='skyblue')
                axes[0, 1].set_title(f'Promedio de {feature} por Cluster')
                axes[0, 1].set_xlabel('Cluster')
                axes[0, 1].set_ylabel('Valor Promedio')
                axes[0, 1].set_xticks(range(len(cluster_means)))
                axes[0, 1].set_xticklabels([f'C{i}' for i in sorted(cluster_analysis.keys())])
            elif i == 1:
                axes[1, 0].bar(range(len(cluster_means)), cluster_means, alpha=0.7,
                              label=feature, color='lightcoral')
                axes[1, 0].set_title(f'Promedio de {feature} por Cluster')
                axes[1, 0].set_xlabel('Cluster')
                axes[1, 0].set_ylabel('Valor Promedio')
                axes[1, 0].set_xticks(range(len(cluster_means)))
                axes[1, 0].set_xticklabels([f'C{i}' for i in sorted(cluster_analysis.keys())])
            elif i == 2:
                axes[1, 1].bar(range(len(cluster_means)), cluster_means, alpha=0.7,
                              label=feature, color='lightgreen')
                axes[1, 1].set_title(f'Promedio de {feature} por Cluster')
                axes[1, 1].set_xlabel('Cluster')
                axes[1, 1].set_ylabel('Valor Promedio')
                axes[1, 1].set_xticks(range(len(cluster_means)))
                axes[1, 1].set_xticklabels([f'C{i}' for i in sorted(cluster_analysis.keys())])

    plt.tight_layout()
    save_plot("10_analisis_clusters_final.png")
    plt.show()

    # Mostrar resultados
    print(f"\n📊 RESULTADOS DEL ANÁLISIS EXPLICATIVO:")
    print(f"   • Precisión árbol ensemble: {dt_ensemble.score(X, y_ensemble):.4f}")
    if dt_dbscan:
        mask_no_noise = y_dbscan != -1
        print(f"   • Precisión árbol DBSCAN: {dt_dbscan.score(X[mask_no_noise], y_dbscan[mask_no_noise]):.4f}")
    print(f"   • Precisión Random Forest: {rf.score(X, y_ensemble):.4f}")

    print(f"\n🏆 TOP 5 CARACTERÍSTICAS MÁS IMPORTANTES:")
    for i, row in feature_importance.head().iterrows():
        print(f"   {i+1}. {row['feature']}: {row['importance']:.4f}")

    print(f"\n📊 ANÁLISIS DE CLUSTERS:")
    for cluster_id in sorted(cluster_analysis.keys()):
        info = cluster_analysis[cluster_id]
        print(f"   • Cluster {cluster_id}: {info['size']} casos ({info['percentage']:.1f}%)")

    return {
        'decision_tree_ensemble': dt_ensemble,
        'decision_tree_dbscan': dt_dbscan,
        'random_forest': rf,
        'feature_importance': feature_importance,
        'cluster_analysis': cluster_analysis
    }

# ============================================================================
# 13. GUARDADO DE MODELOS CON ÉNFASIS EN DBSCAN
# ============================================================================

def save_models_dbscan_focused(results_optimized, ensemble_result, explanatory_analysis, scaler, label_encoders):
    """
    Guarda modelos con énfasis especial en DBSCAN
    """
    print("\n" + "="*50)
    print("💾 GUARDADO DE MODELOS - ENFOQUE DBSCAN")
    print("="*50)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Preparar datos del modelo completo con énfasis en DBSCAN
    model_data = {
        'main_algorithm': 'DBSCAN',
        'dbscan_optimized': results_optimized.get('dbscan_optimized'),
        'ensemble_with_dbscan_emphasis': ensemble_result,
        'all_clustering_results': results_optimized,
        'explanatory_analysis': {
            'feature_importance': explanatory_analysis['feature_importance'].to_dict('records'),
            'cluster_analysis': explanatory_analysis['cluster_analysis'],
            'decision_tree_ensemble': explanatory_analysis['decision_tree_ensemble'],
            'decision_tree_dbscan': explanatory_analysis['decision_tree_dbscan'],
            'random_forest': explanatory_analysis['random_forest']
        },
        'preprocessing': {
            'scaler': scaler,
            'label_encoders': label_encoders
        },
        'metadata': {
            'timestamp': timestamp,
            'version': '2.0_DBSCAN_FOCUSED',
            'description': 'Modelo de clustering con DBSCAN como algoritmo principal y ensemble optimizado',
            'main_algorithm': 'DBSCAN',
            'ensemble_weights': ensemble_result.get('weights', {}),
            'dbscan_emphasis': True
        }
    }

    saved_files = []

    # 1. Modelo principal DBSCAN (.pkl)
    try:
        dbscan_filename = f"dbscan_model_optimized_{timestamp}.pkl"
        dbscan_data = {
            'model': results_optimized['dbscan_optimized']['model'],
            'labels': results_optimized['dbscan_optimized']['labels'],
            'metrics': {
                'silhouette': results_optimized['dbscan_optimized']['silhouette'],
                'calinski_harabasz': results_optimized['dbscan_optimized']['calinski_harabasz'],
                'davies_bouldin': results_optimized['dbscan_optimized']['davies_bouldin'],
                'n_clusters': results_optimized['dbscan_optimized']['n_clusters'],
                'n_noise': results_optimized['dbscan_optimized']['n_noise']
            },
            'optimization_params': results_optimized['dbscan_optimized']['optimization'],
            'preprocessing': {'scaler': scaler, 'label_encoders': label_encoders}
        }

        with open(dbscan_filename, 'wb') as f:
            pickle.dump(dbscan_data, f)
        saved_files.append(dbscan_filename)
        print(f"✅ DBSCAN principal guardado: {dbscan_filename}")
    except Exception as e:
        print(f"❌ Error guardando DBSCAN principal: {e}")

    # 2. Modelo completo (.joblib)
    try:
        complete_filename = f"clustering_complete_dbscan_focused_{timestamp}.joblib"
        joblib.dump(model_data, complete_filename)
        saved_files.append(complete_filename)
        print(f"✅ Modelo completo guardado: {complete_filename}")
    except Exception as e:
        print(f"❌ Error guardando modelo completo: {e}")

    # 3. Ensemble con énfasis DBSCAN (.h5) - Solo si h5py está disponible
    if HAS_H5PY:
        try:
            ensemble_filename = f"ensemble_dbscan_emphasis_{timestamp}.h5"
            with h5py.File(ensemble_filename, 'w') as f:
                # Datos del ensemble
                ens_grp = f.create_group('ensemble')
                ens_grp.create_dataset('labels', data=ensemble_result['labels'])
                ens_grp.create_dataset('silhouette', data=ensemble_result['silhouette'])
                ens_grp.create_dataset('n_clusters', data=ensemble_result['n_clusters'])

                # Datos de DBSCAN
                if 'dbscan_optimized' in results_optimized:
                    dbscan_grp = f.create_group('dbscan_optimized')
                    dbscan_grp.create_dataset('labels', data=results_optimized['dbscan_optimized']['labels'])
                    dbscan_grp.create_dataset('silhouette', data=results_optimized['dbscan_optimized']['silhouette'])
                    dbscan_grp.create_dataset('n_clusters', data=results_optimized['dbscan_optimized']['n_clusters'])
                    dbscan_grp.create_dataset('n_noise', data=results_optimized['dbscan_optimized']['n_noise'])

                # Metadatos
                f.attrs['main_algorithm'] = 'DBSCAN'
                f.attrs['timestamp'] = timestamp
                f.attrs['dbscan_emphasis'] = True

            saved_files.append(ensemble_filename)
            print(f"✅ Ensemble con énfasis DBSCAN guardado: {ensemble_filename}")
        except Exception as e:
            print(f"❌ Error guardando ensemble: {e}")
    else:
        print(f"⚠️ Saltando guardado HDF5 - h5py no disponible")

    # 4. Metadatos y resultados (.json)
    try:
        json_filename = f"dbscan_results_summary_{timestamp}.json"

        # Convertir valores numpy a tipos Python estándar para JSON
        def convert_to_python_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            else:
                return obj

        json_data = {
            'project_info': {
                'main_algorithm': 'DBSCAN',
                'timestamp': timestamp,
                'version': '2.0_DBSCAN_FOCUSED',
                'description': 'Proyecto académico de clustering con DBSCAN como ganador'
            },
            'dbscan_results': {
                'optimization_params': results_optimized['dbscan_optimized']['optimization'],
                'metrics': {
                    'silhouette': convert_to_python_types(results_optimized['dbscan_optimized']['silhouette']),
                    'calinski_harabasz': convert_to_python_types(results_optimized['dbscan_optimized']['calinski_harabasz']),
                    'davies_bouldin': convert_to_python_types(results_optimized['dbscan_optimized']['davies_bouldin']),
                    'n_clusters': convert_to_python_types(results_optimized['dbscan_optimized']['n_clusters']),
                    'n_noise': convert_to_python_types(results_optimized['dbscan_optimized']['n_noise']),
                    'noise_percentage': convert_to_python_types(results_optimized['dbscan_optimized']['noise_percentage'])
                }
            },
            'ensemble_results': {
                'silhouette': convert_to_python_types(ensemble_result['silhouette']),
                'calinski_harabasz': convert_to_python_types(ensemble_result['calinski_harabasz']),
                'davies_bouldin': convert_to_python_types(ensemble_result['davies_bouldin']),
                'n_clusters': convert_to_python_types(ensemble_result['n_clusters']),
                'dbscan_emphasis': True,
                'weights': {k: convert_to_python_types(v) for k, v in ensemble_result['weights'].items()}
            },
            'feature_importance': explanatory_analysis['feature_importance'].to_dict('records'),
            'cluster_analysis': {
                str(k): {
                    'size': convert_to_python_types(v['size']),
                    'percentage': convert_to_python_types(v['percentage'])
                } for k, v in explanatory_analysis['cluster_analysis'].items()
            }
        }

        with open(json_filename, 'w') as f:
            json.dump(json_data, f, indent=2)

        saved_files.append(json_filename)
        print(f"✅ Resumen de resultados guardado: {json_filename}")
    except Exception as e:
        print(f"❌ Error guardando resumen JSON: {e}")

    print(f"\n✅ Modelos guardados exitosamente con énfasis en DBSCAN")
    print(f"📁 Archivos creados: {len(saved_files)}")
    for file in saved_files:
        print(f"   • {file}")

    return saved_files

# ============================================================================
# 14. FUNCIÓN PRINCIPAL MODIFICADA - ENFOQUE DBSCAN
# ============================================================================

def run_complete_clustering_project_dbscan():
    """
    Ejecuta el proyecto completo de clustering con DBSCAN como algoritmo principal
    """
    print("🚀 PROYECTO COMPLETO DE CLUSTERING - DBSCAN COMO GANADOR")
    print("=" * 70)

    try:
        # PASO 1: Obtener dataset
        print("\n📊 PASO 1: OBTENCIÓN DE DATASET")
        df, dataset_info = get_dataset()

        # PASO 2: Análisis exploratorio
        print("\n📊 PASO 2: ANÁLISIS EXPLORATORIO")
        eda_results = exploratory_data_analysis(df)

        # PASO 3: Preprocesamiento
        print("\n🔧 PASO 3: PREPROCESAMIENTO")
        X_scaled, feature_names, scaler, label_encoders, df_processed = preprocess_data(df)

        # PASO 4: Clustering inicial (ANTES)
        print("\n🎯 PASO 4: CLUSTERING INICIAL")
        results_initial = apply_initial_clustering(X_scaled, feature_names)

        # PASO 5: Visualización inicial
        print("\n📊 PASO 5: VISUALIZACIÓN INICIAL")
        X_pca, X_tsne, pca = visualize_initial_clusters(X_scaled, results_initial, df)

        # PASO 6: Identificar limitaciones
        print("\n🔍 PASO 6: IDENTIFICACIÓN DE LIMITACIONES")
        limitations = identify_limitations(results_initial, X_scaled, df)

        # PASO 7: Optimización con énfasis en DBSCAN
        print("\n⚡ PASO 7: OPTIMIZACIÓN CON ÉNFASIS EN DBSCAN")
        results_optimized = optimize_clustering(X_scaled, feature_names, results_initial)

        # PASO 8: Ensemble con énfasis en DBSCAN
        print("\n🎭 PASO 8: ENSEMBLE CON ÉNFASIS EN DBSCAN")
        ensemble_result = ensemble_clustering_dbscan_focused(X_scaled, results_optimized)

        # PASO 9: Comparación antes vs después (enfoque DBSCAN)
        print("\n📊 PASO 9: COMPARACIÓN ANTES VS DESPUÉS")
        compare_before_after_dbscan(results_initial, results_optimized, ensemble_result, X_pca)

        # PASO 10: Análisis explicativo (enfoque DBSCAN)
        print("\n🌳 PASO 10: ANÁLISIS EXPLICATIVO")
        explanatory_results = explanatory_analysis_dbscan(X_scaled, feature_names, ensemble_result,
                                                         results_optimized, df)

        # PASO 11: Guardar modelos (enfoque DBSCAN)
        print("\n💾 PASO 11: GUARDADO DE MODELOS")
        saved_files = save_models_dbscan_focused(results_optimized, ensemble_result,
                                               explanatory_results, scaler, label_encoders)

        # RESUMEN FINAL CON ÉNFASIS EN DBSCAN
        print("\n" + "="*70)
        print("🎉 PROYECTO COMPLETADO - DBSCAN COMO GANADOR")
        print("="*70)

        # Resultados de DBSCAN
        if 'dbscan_optimized' in results_optimized:
            dbscan_result = results_optimized['dbscan_optimized']
            ensemble_score = ensemble_result['silhouette']

            print(f"📊 RESULTADOS FINALES:")
            print(f"   • Dataset: {dataset_info['filename']} ({dataset_info['source']})")
            print(f"   • DBSCAN optimizado: {dbscan_result['silhouette']:.4f}")
            print(f"   • Ensemble + DBSCAN: {ensemble_score:.4f}")
            print(f"   • Clusters finales: {ensemble_result['n_clusters']}")
            print(f"   • Ruido en DBSCAN: {dbscan_result['n_noise']} puntos ({dbscan_result['noise_percentage']:.1f}%)")
            print(f"   • Parámetros DBSCAN: {dbscan_result['optimization']}")

            improvement = ((ensemble_score - dbscan_result['silhouette']) / dbscan_result['silhouette'] * 100)
            print(f"   • Mejora con ensemble: {improvement:+.2f}%")

        print(f"\n🎯 VENTAJAS DE DBSCAN IDENTIFICADAS:")
        print(f"   • Detecta automáticamente el número de clusters")
        print(f"   • Identifica y maneja puntos de ruido (outliers)")
        print(f"   • No asume forma esférica de clusters")
        print(f"   • Robusto a diferentes densidades")

        print(f"\n✅ MEJORAS IMPLEMENTADAS:")
        print(f"   • Optimización mejorada de parámetros DBSCAN")
        print(f"   • Estimación inteligente del rango de eps usando k-nearest neighbors")
        print(f"   • Ensemble clustering con peso doble para DBSCAN")
        print(f"   • Análisis específico de ruido y outliers")
        print(f"   • Comparación detallada antes vs después")
        print(f"   • Guardado especializado para DBSCAN")

        return {
            'dataset': df,
            'dataset_info': dataset_info,
            'results_initial': results_initial,
            'results_optimized': results_optimized,
            'ensemble_result': ensemble_result,
            'explanatory_results': explanatory_results,
            'saved_files': saved_files,
            'main_algorithm': 'DBSCAN'
        }

    except Exception as e:
        print(f"❌ Error durante la ejecución: {e}")
        import traceback
        traceback.print_exc()
        return None

# ============================================================================
# 15. EJECUCIÓN AUTOMÁTICA CON ENFOQUE DBSCAN
# ============================================================================

if __name__ == "__main__":
    print("🎯 PROYECTO ACADÉMICO DE CLUSTERING - DBSCAN GANADOR")
    print("📋 Análisis completo con DBSCAN como algoritmo principal")
    print("🔬 Incluye: Búsqueda automática, EDA, Optimización DBSCAN, Ensemble, Análisis Explicativo")
    print("🏆 DBSCAN es el algoritmo GANADOR según especificaciones")
    print("\n" + "="*70)

    # Ejecutar proyecto completo con enfoque en DBSCAN
    results = run_complete_clustering_project_dbscan()

    if results:
        print(f"\n🎊 ¡PROYECTO COMPLETADO CON ÉXITO!")
        print(f"🏆 DBSCAN confirmado como algoritmo GANADOR")
        print(f"📊 Ensemble clustering mejora los resultados de DBSCAN")
        print(f"📁 Revisa los archivos generados y las visualizaciones")
        print(f"📂 Dataset: {results['dataset_info']['filename']} ({results['dataset_info']['source']})")

        if 'dbscan_optimized' in results['results_optimized']:
            dbscan_metrics = results['results_optimized']['dbscan_optimized']
            print(f"🎯 DBSCAN final: {dbscan_metrics['silhouette']:.4f} silhouette, {dbscan_metrics['n_clusters']} clusters")
    else:
        print(f"\n❌ El proyecto no pudo completarse")

    print(f"\n" + "="*70)
    print("📚 PROYECTO LISTO PARA PRESENTACIÓN ACADÉMICA")
    print("🏆 DBSCAN COMO ALGORITMO PRINCIPAL DEMOSTRADO")
    print("="*70)