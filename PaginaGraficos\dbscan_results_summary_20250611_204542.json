{"project_info": {"main_algorithm": "DBSCAN", "timestamp": "20250611_204542", "version": "2.0_DBSCAN_FOCUSED", "description": "Proyecto académico de clustering con DBSCAN como ganador"}, "dbscan_results": {"optimization_params": "parámetros iniciales (eps=0.5, min_samples=5)", "metrics": {"silhouette": 0.8304821727503416, "calinski_harabasz": 1359.8892248224818, "davies_bouldin": 0.24405731224366406, "n_clusters": 25, "n_noise": 1462, "noise_percentage": 88.39177750906893}}, "ensemble_results": {"silhouette": 0.5100685102375991, "calinski_harabasz": 1155.2958183942321, "davies_bouldin": 0.9086668619838282, "n_clusters": 2, "dbscan_emphasis": true, "weights": {"dbscan_optimized": 0.5205017354306192, "kmeans_optimized": 0.15666186769711488, "gmm_optimized": 0.15984180845685209, "agglomerative": 0.16299458841541392}}, "feature_importance": [{"feature": "Age_Mons", "importance": 0.8269648324568872}, {"feature": "Qchat-10-Score", "importance": 0.03496360226403122}, {"feature": "Who completed the test", "importance": 0.0289868618905946}, {"feature": "Class/ASD Traits ", "importance": 0.015960726073410075}, {"feature": "Ethnicity", "importance": 0.015346917782703527}, {"feature": "A5", "importance": 0.009584078471834043}, {"feature": "A2", "importance": 0.009121913872143346}, {"feature": "A9", "importance": 0.007620494557643904}, {"feature": "A1", "importance": 0.006357080761624896}, {"feature": "A7", "importance": 0.005883452022061323}, {"feature": "Family_mem_with_ASD", "importance": 0.005858667917446797}, {"feature": "Jaundice", "importance": 0.005807434082088349}, {"feature": "A4", "importance": 0.005741344742982924}, {"feature": "Sex", "importance": 0.005606469596469715}, {"feature": "A6", "importance": 0.00507529295630594}, {"feature": "A3", "importance": 0.004292018356332646}, {"feature": "A10", "importance": 0.0035233349075723796}, {"feature": "A8", "importance": 0.0033054772878671024}], "cluster_analysis": {"0": {"size": 1414, "percentage": 85.48972188633616}, "1": {"size": 240, "percentage": 14.510278113663846}}}