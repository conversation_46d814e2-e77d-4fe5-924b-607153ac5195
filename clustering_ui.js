/**
 *  Clustering UI - Script para añadir visualizaciones de clustering con combobox
 */

// Configuración
const API_BASE_URL = "http://127.0.0.1:5000"
let currentUserData = null
let currentVisualization = null

// Función para inicializar la UI de clustering
function initClusteringUI() {
  console.log("🧠 Inicializando UI de Clustering...")

  // Obtener los datos del usuario actual
  currentUserData = window.formData || {}

  // Buscar el contenedor donde insertar la UI de clustering
  const resultsContent = document.getElementById("results-content")

  if (!resultsContent) {
    console.error("❌ No se encontró el contenedor de resultados")
    return
  }

  // Crear el contenedor para la UI de clustering
  const clusteringContainer = document.createElement("div")
  clusteringContainer.id = "clustering-visualization-container"
  clusteringContainer.className = "visualizations-card"

  // Añadir HTML para el combobox y la visualización
  clusteringContainer.innerHTML = `
    <div class="visualizations-header">
      <i class="fas fa-chart-scatter"></i>
      <h3>Visualización de Clustering</h3>
    </div>
    <div class="visualizations-content">
      <div class="k-selector-section">
        <h4>🎯 Configuración de Grupos de Riesgo:</h4>
        <div class="k-selector">
          <label for="k-select">Número de Clases de Riesgo:</label>
          <select id="k-select" onchange="updateClusteringK()" class="k-select-enhanced">
            <option value="2">K=2 - CON AUTISMO vs SIN AUTISMO (Binario)</option>
            <option value="3" selected>K=3 - BAJO | MODERADO | ALTO RIESGO (Tres Niveles)</option>
          </select>
          <div class="k-info-enhanced">
            <span class="k-current">K actual: <span id="current-k">3</span></span>
          </div>
        </div>
        <div class="k-explanation">
          <p id="k-explanation-text"><strong>K=3:</strong> Clasificación en tres niveles: <span class="highlight-green">BAJO RIESGO</span>, <span class="highlight-orange">RIESGO MODERADO</span> y <span class="highlight-red">ALTO RIESGO</span>.</p>
        </div>
      </div>
      
      <div id="visualization-loading" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Actualizando clustering...</p>
        </div>
      </div>
      
      <div id="visualization-container" class="visualization-item enhanced">
        <h4>🎯 Clustering de Grupos de Riesgo (<span id="visualization-k">K=3</span>)</h4>
        <div class="plot-container enhanced">
          <img id="visualization-image" class="clustering-plot-image" src="/placeholder.svg" alt="Clustering de Riesgo" style="display: none;" />
          <div id="visualization-placeholder" class="plot-placeholder">
            <i class="fas fa-chart-scatter"></i>
            <p>Cargando visualización de clustering...</p>
          </div>
        </div>
        <div class="plot-description enhanced">
          <p><strong>Su hijo/a está representado por la ⭐ amarilla.</strong></p>
          <p id="color-explanation">Los colores indican: <span class="color-green">Verde = Bajo Riesgo</span> | <span class="color-orange">Naranja = Riesgo Moderado</span> | <span class="color-red">Rojo = Alto Riesgo</span></p>
          <p>Su posición relativa muestra la similitud con otros casos históricos del dataset.</p>
        </div>
      </div>
      
      <div id="visualization-error" class="error-message" style="display: none;">
        <i class="fas fa-exclamation-triangle"></i>
        <span>Error al generar la visualización. Por favor, intente nuevamente.</span>
      </div>
    </div>
  `

  // Insertar el contenedor antes de la nota importante
  const importantNote = document.querySelector(".important-note")
  if (importantNote) {
    resultsContent.insertBefore(clusteringContainer, importantNote)
  } else {
    resultsContent.appendChild(clusteringContainer)
  }

  // Generar visualización inicial
  generateVisualization(3)
}

// Generar visualización de clustering
async function generateVisualization(k) {
  console.log(`📊 Generando visualización con k=${k}...`)

  // Actualizar UI
  document.getElementById("current-k").textContent = k
  document.getElementById("visualization-k").textContent = `K=${k}`

  if (k == 2) {
    document.getElementById("k-explanation-text").innerHTML =
      '<strong>K=2:</strong> Clasificación binaria que divide los casos en <span class="highlight-green">SIN AUTISMO</span> y <span class="highlight-red">CON AUTISMO</span>.'
    document.getElementById("color-explanation").innerHTML =
      'Los colores indican: <span class="color-green">Verde = Sin Autismo (Bajo Riesgo)</span> | <span class="color-red">Rojo = Con Autismo (Alto Riesgo)</span>'
  } else {
    document.getElementById("k-explanation-text").innerHTML =
      '<strong>K=3:</strong> Clasificación en tres niveles: <span class="highlight-green">BAJO RIESGO</span>, <span class="highlight-orange">RIESGO MODERADO</span> y <span class="highlight-red">ALTO RIESGO</span>.'
    document.getElementById("color-explanation").innerHTML =
      'Los colores indican: <span class="color-green">Verde = Bajo Riesgo</span> | <span class="color-orange">Naranja = Riesgo Moderado</span> | <span class="color-red">Rojo = Alto Riesgo</span>'
  }

  // Mostrar indicador de carga
  const loadingElement = document.getElementById("visualization-loading")
  const placeholderElement = document.getElementById("visualization-placeholder")
  const imageElement = document.getElementById("visualization-image")
  const errorElement = document.getElementById("visualization-error")

  if (loadingElement) loadingElement.style.display = "flex"
  if (placeholderElement) placeholderElement.style.display = "flex"
  if (imageElement) imageElement.style.display = "none"
  if (errorElement) errorElement.style.display = "none"

  try {
    // Preparar datos con k seleccionado
    const requestData = { ...window.formData, selected_k: k }

    // Intentar usar el endpoint específico de visualización
    let endpoint = `${API_BASE_URL}/clustering_visualization`
    let response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    })

    // Si falla, intentar con el endpoint de predicción
    if (!response.ok) {
      console.log("⚠️ Endpoint de visualización no disponible, intentando con /predict")
      endpoint = `${API_BASE_URL}/predict`
      response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })
    }

    if (response.ok) {
      const result = await response.json()

      // Buscar la visualización en diferentes ubicaciones de la respuesta
      let visualization = null

      if (result.visualization) {
        visualization = result.visualization
      } else if (result.clustering_visualization) {
        visualization = result.clustering_visualization
      } else if (result.clustering_analysis && result.clustering_analysis.clustering_plot) {
        visualization = result.clustering_analysis.clustering_plot
      }

      if (visualization) {
        // Actualizar imagen
        if (imageElement) {
          imageElement.src = visualization
          imageElement.style.display = "block"
          placeholderElement.style.display = "none"
          currentVisualization = visualization
          console.log("✅ Visualización actualizada")

          // Mostrar notificación de éxito
          showNotification(`Clustering actualizado exitosamente con K=${k}`, "success")
        }
      } else {
        throw new Error("No se encontró visualización en la respuesta")
      }
    } else {
      throw new Error(`Error en la solicitud: ${response.status}`)
    }
  } catch (error) {
    console.error("❌ Error generando visualización:", error)
    if (errorElement) errorElement.style.display = "block"
    showNotification("Error al actualizar el clustering", "error")
  } finally {
    // Ocultar indicador de carga
    if (loadingElement) loadingElement.style.display = "none"
  }
}

// Función para mostrar notificaciones (reutiliza la existente)
function showNotification(message, type = "info") {
  // Verificar si ya existe la función en el código principal
  if (typeof window.showNotification === "function") {
    window.showNotification(message, type)
  } else {
    // Implementación básica si no existe
    const notification = document.createElement("div")
    notification.className = `notification ${type}`
    notification.innerHTML = `
      <i class="fas fa-${type === "success" ? "check-circle" : type === "error" ? "exclamation-circle" : "info-circle"}"></i>
      <span>${message}</span>
    `

    document.body.appendChild(notification)

    // Auto-remover después de 3 segundos
    setTimeout(() => {
      notification.remove()
    }, 3000)
  }
}

// Función para actualizar el clustering con nuevo K
function updateClusteringK() {
  const kSelect = document.getElementById("k-select")
  const selectedK = Number.parseInt(kSelect.value)
  generateVisualization(selectedK)
}

// Inicializar cuando el DOM esté listo
document.addEventListener("DOMContentLoaded", () => {
  // Esperar un poco para asegurarse de que la página de resultados esté cargada
  setTimeout(() => {
    if (document.getElementById("results-view").classList.contains("active")) {
      initClusteringUI()
    } else {
      // Configurar un observador para detectar cuando se muestre la vista de resultados
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.target.id === "results-view" && mutation.target.classList.contains("active")) {
            initClusteringUI()
            observer.disconnect()
          }
        })
      })

      observer.observe(document.getElementById("results-view"), {
        attributes: true,
        attributeFilter: ["class"],
      })
    }
  }, 500)
})

console.log("🧠 Script de UI de Clustering cargado")
