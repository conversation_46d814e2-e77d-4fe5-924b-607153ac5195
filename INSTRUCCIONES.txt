========================================
  PROYECTO AUSTIMO - INSTRUCCIONES
========================================

ARCHIVOS CREADOS:
- instalar_proyecto.bat     : Instala todas las dependencias
- verificar_dependencias.bat: Verifica que todo esté instalado
- ejecutar_proyecto.bat     : Ejecuta todo el proyecto completo
- ejecutar_rapido.bat       : Versión rápida sin mensajes detallados

========================================
  COMO EJECUTAR EL PROYECTO
========================================

PRIMERA VEZ (INSTALACIÓN):
1. Haz doble clic en: instalar_proyecto.bat
2. Espera a que termine la instalación
3. Haz doble clic en: ejecutar_proyecto.bat

EJECUCIONES POSTERIORES:
- Haz doble clic en: ejecutar_proyecto.bat
- O usa: ejecutar_rapido.bat (más rápido)

VERIFICAR PROBLEMAS:
- Haz doble clic en: verificar_dependencias.bat

========================================
  QUE HACE CADA ARCHIVO
========================================

instalar_proyecto.bat:
- Verifica que Python esté instalado
- Instala pandas, numpy, matplotlib, seaborn
- Instala scikit-learn, scipy, flask, plotly
- Verifica que todo se instaló correctamente

ejecutar_proyecto.bat:
- Ejecuta PaginaGraficos/generar_graficos.bat
- Ejecuta clustering_visualizer.py
- Ejecuta app.py en segundo plano
- Abre index.html en el navegador

========================================
  SOLUCIÓN DE PROBLEMAS
========================================

Si algo no funciona:
1. Ejecuta: verificar_dependencias.bat
2. Si faltan dependencias: instalar_proyecto.bat
3. Si persisten errores: revisa que todos los archivos estén presentes

Archivos necesarios:
- datos.csv
- index.html
- clustering_visualizer.py
- app.py
- PaginaGraficos/graficostablas.py
- PaginaGraficos/generar_graficos.bat

========================================
  NOTAS IMPORTANTES
========================================

- Requiere Python instalado en el sistema
- Los gráficos se generan en PaginaGraficos/graficos_generados/
- El servidor web se ejecuta en segundo plano
- Para detener el servidor, cierra la ventana de comandos
- La página web se abre automáticamente en el navegador

========================================
