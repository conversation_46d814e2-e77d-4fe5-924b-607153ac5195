@echo off
echo ========================================
echo  EJECUTOR PROYECTO AUSTIMO
echo ========================================
echo.
echo Iniciando secuencia automatica...
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Verificar dependencias rapidamente
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python no detectado
    echo Ejecuta primero: Instalador\instalar_proyecto.bat
    pause
    exit /b 1
)

REM PASO 1: Generar graficos
echo ========================================
echo  PASO 1: GENERANDO GRAFICOS
echo ========================================
echo.
echo Ejecutando generacion de graficos...
cd PaginaGraficos
call generar_graficos.bat
if errorlevel 1 (
    echo ERROR: Fallo en la generacion de graficos
    cd ..\Instalador
    pause
    exit /b 1
)
cd ..
echo.
echo ✓ Graficos generados exitosamente
echo.

REM PASO 2: Ejecutar clustering_visualizer.py
echo ========================================
echo  PASO 2: EJECUTANDO CLUSTERING VISUALIZER
echo ========================================
echo.
echo Ejecutando clustering_visualizer.py...
python clustering_visualizer.py
if errorlevel 1 (
    echo ERROR: Fallo en clustering_visualizer.py
    cd Instalador
    pause
    exit /b 1
)
echo.
echo ✓ Clustering visualizer ejecutado exitosamente
echo.

REM PASO 3: Ejecutar app.py en segundo plano
echo ========================================
echo  PASO 3: INICIANDO SERVIDOR WEB
echo ========================================
echo.
echo Iniciando app.py en segundo plano...
start /B python app.py
echo.
echo ✓ Servidor web iniciado
echo Esperando 3 segundos para que el servidor se inicie...
timeout /t 3 /nobreak >nul
echo.

REM PASO 4: Abrir index.html en el navegador
echo ========================================
echo  PASO 4: ABRIENDO PAGINA WEB
echo ========================================
echo.
echo Abriendo index.html en el navegador...
start "" "index.html"
echo.
echo ✓ Pagina web abierta en el navegador
echo.

echo ========================================
echo  PROYECTO EJECUTADO EXITOSAMENTE
echo ========================================
echo.
echo ✓ Graficos generados
echo ✓ Scripts Python ejecutados
echo ✓ Servidor web iniciado
echo ✓ Pagina web abierta
echo.
echo El proyecto esta funcionando completamente.
echo.
echo NOTA: Para detener el servidor web, cierra esta ventana
echo      o presiona Ctrl+C si ves procesos Python ejecutandose.
echo.
cd Instalador
pause
