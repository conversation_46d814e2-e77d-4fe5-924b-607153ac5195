@echo off
echo ========================================
echo  EJECUTOR PROYECTO AUSTIMO
echo ========================================
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Verificacion rapida de Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python no detectado
    echo   Ejecuta: Instalador\verificar_e_instalar.bat
    cd Instalador
    pause
    exit /b 1
)

echo ✓ Iniciando proyecto...
echo.

REM PASO 1: Generar graficos
echo [1/4] Generando graficos...
echo          Esto puede tomar 1-2 minutos, por favor espera...
cd PaginaGraficos
call generar_graficos.bat
if errorlevel 1 (
    echo ✗ Error generando graficos
    cd ..\Instalador
    pause
    exit /b 1
)
cd ..
echo ✓ Graficos generados

REM PASO 2: Ejecutar clustering_visualizer.py
echo [2/4] Ejecutando clustering visualizer...
echo          Procesando datos de clustering...
python clustering_visualizer.py
if errorlevel 1 (
    echo ✗ Error en clustering visualizer
    cd Instalador
    pause
    exit /b 1
)
echo ✓ Clustering completado

REM PASO 3: Iniciar servidor web en segundo plano
echo [3/4] Iniciando servidor web...
start /B python app.py
echo ✓ Servidor iniciado

REM PASO 4: Esperar que el servidor este listo y abrir navegador
echo [4/4] Esperando servidor y abriendo navegador...
echo          Esperando 5 segundos para que app.py se inicie completamente...

REM Esperar 5 segundos para que app.py se inicie
timeout /t 5 /nobreak >nul

REM Abrir navegador
start "" "index.html"
echo ✓ Navegador abierto

echo.
echo ========================================
echo  PROYECTO EJECUTADO EXITOSAMENTE
echo ========================================
echo.
echo ✓ Graficos generados en PaginaGraficos\graficos_generados\
echo ✓ Scripts Python ejecutados correctamente
echo ✓ Servidor web funcionando en segundo plano
echo ✓ Pagina web abierta en el navegador
echo.
echo NOTA: Para detener el servidor, cierra esta ventana
echo.
cd Instalador
pause
