@echo off
echo ========================================
echo  EJECUTOR RAPIDO - PROYECTO AUSTIMO
echo ========================================
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Generar graficos
echo Generando graficos...
cd PaginaGraficos && call generar_graficos.bat && cd ..

REM Ejecutar scripts
echo Ejecutando clustering_visualizer...
python clustering_visualizer.py

echo Iniciando servidor web...
start /B python app.py

REM Esperar y abrir navegador
timeout /t 2 /nobreak >nul
echo Abriendo navegador...
start "" "index.html"

echo.
echo ✓ Proyecto ejecutado exitosamente
echo ✓ Pagina web abierta en el navegador
echo.
cd Instalador
pause
