# 🧠 Proyecto de Análisis de Clustering - Austimo

## 📋 Descripción
Este proyecto realiza un análisis completo de clustering sobre datos de screening de autismo utilizando el algoritmo DBSCAN como método principal. Los gráficos generados se integran automáticamente en una página web para visualización.

## 📁 Estructura del Proyecto
```
PaginaGraficos/
├── datos.csv                    # Dataset principal (1,654 registros)
├── graficostablas.py            # Script principal de análisis
├── index.html                   # Página web con visualizaciones
├── graficos_generados/          # Carpeta con gráficos generados
│   ├── 01_eda_distribuciones_numericas.png
│   ├── 02_eda_variables_categoricas.png
│   ├── 03_eda_matriz_correlacion.png
│   ├── 04_eda_analisis_grupos_riesgo.png
│   ├── 05_clustering_inicial_pca.png
│   ├── 06_clustering_inicial_tsne.png
│   ├── 07_comparacion_antes_despues.png
│   ├── 08_importancia_caracteristicas.png
│   ├── 09_arboles_decision_comparacion.png
│   └── 10_analisis_clusters_final.png
├── generar_graficos.bat         # Script principal para regenerar gráficos
├── generar_graficos_simple.bat # Script alternativo (menos dependencias)
├── graficos_simple.py           # Versión simplificada del análisis
├── instalar_dependencias.bat   # Instalador automático de librerías
└── README.md                    # Este archivo
```

## 🚀 Uso Rápido

### Opción 1: Script Automático (Windows)
```bash
# Ejecutar el archivo batch
generar_graficos.bat
```

### Opción 2: Comando Manual
```bash
# Navegar al directorio
cd PaginaGraficos

# Ejecutar el script Python
python graficostablas.py

# Abrir la página web
# Abrir index.html en el navegador
```

## 📊 Características del Análisis

### Algoritmos Implementados
- **DBSCAN** (Algoritmo principal/ganador)
- K-Means (comparación)
- Gaussian Mixture Model (comparación)
- Agglomerative Clustering (comparación)
- Ensemble Clustering con énfasis en DBSCAN

### Visualizaciones Generadas
1. **EDA**: Distribuciones numéricas y categóricas
2. **Correlaciones**: Matriz de correlación entre variables
3. **Clustering**: Visualizaciones PCA y t-SNE
4. **Comparaciones**: Antes vs después de optimización
5. **Explicabilidad**: Importancia de características y árboles de decisión

### Métricas de Evaluación
- Silhouette Score
- Calinski-Harabasz Index
- Davies-Bouldin Index
- Adjusted Rand Index
- Normalized Mutual Information

## 📈 Resultados Principales
- **DBSCAN Optimizado**: 0.8305 silhouette score
- **25 clusters** identificados automáticamente
- **88.4% de ruido** detectado (característica normal de DBSCAN)
- **Ensemble con énfasis DBSCAN**: 0.5101 silhouette score

## 🔧 Requisitos
- Python 3.7+
- Librerías: pandas, numpy, matplotlib, seaborn, scikit-learn, scipy, h5py, joblib

## 🚨 Solución de Problemas

### Error: "No module named 'h5py'"
**Solución 1 (Recomendada):**
```bash
# Instalar dependencias automáticamente
instalar_dependencias.bat
```

**Solución 2 (Manual):**
```bash
pip install h5py pandas numpy matplotlib seaborn scikit-learn scipy joblib
```

**Solución 3 (Versión Simplificada):**
```bash
# Usar la versión que no requiere h5py
generar_graficos_simple.bat
```

## 📝 Dataset
- **Archivo**: datos.csv
- **Registros**: 1,654
- **Variables**: 19 (incluyendo A1-A10, edad, demografía)
- **Tipo**: Datos de screening de autismo Q-Chat-10

## 🌐 Visualización Web
Abre `index.html` en tu navegador para ver:
- Página web responsiva con todos los gráficos
- Estadísticas del proyecto
- Navegación intuitiva entre visualizaciones
- Información detallada de cada gráfico

## 📧 Notas
- Los gráficos se regeneran automáticamente cada vez que ejecutas el script
- La página web se actualiza automáticamente con nuevos gráficos
- Todos los modelos entrenados se guardan automáticamente
