@echo off
echo ========================================
echo  VERIFICADOR E INSTALADOR - AUSTIMO
echo ========================================
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ ERROR: Python no esta instalado
    echo   Descarga Python desde: https://python.org
    echo   Asegurate de marcar "Add to PATH" durante la instalacion
    echo.
    cd Instalador
    pause
    exit /b 1
)

echo ✓ Python detectado
echo.

REM Lista de dependencias necesarias
set "dependencias=pandas numpy matplotlib seaborn scikit-learn scipy flask plotly joblib h5py"
set "faltantes="

echo Verificando dependencias...

REM Verificar cada dependencia
for %%d in (%dependencias%) do (
    python -c "import %%d" 2>nul
    if errorlevel 1 (
        echo ✗ %%d NO instalado
        set "faltantes=!faltantes! %%d"
    ) else (
        echo ✓ %%d instalado
    )
)

REM Si hay dependencias faltantes, instalarlas
if defined faltantes (
    echo.
    echo ========================================
    echo  INSTALANDO DEPENDENCIAS FALTANTES
    echo ========================================
    echo.
    echo Actualizando pip...
    python -m pip install --upgrade pip
    
    echo.
    echo Instalando dependencias faltantes...
    pip install%faltantes%
    
    echo.
    echo Verificando instalacion...
    set "errores="
    for %%d in (%dependencias%) do (
        python -c "import %%d" 2>nul
        if errorlevel 1 (
            echo ✗ Error instalando %%d
            set "errores=1"
        ) else (
            echo ✓ %%d instalado correctamente
        )
    )
    
    if defined errores (
        echo.
        echo ✗ Algunas dependencias no se pudieron instalar
        echo   Intenta ejecutar como administrador
        cd Instalador
        pause
        exit /b 1
    )
) else (
    echo.
    echo ✓ Todas las dependencias ya estan instaladas
)

echo.
echo Verificando archivos del proyecto...

REM Verificar archivos principales
set "archivos_faltantes="
if not exist "datos.csv" (echo ✗ datos.csv NO encontrado & set "archivos_faltantes=1")
if not exist "index.html" (echo ✓ index.html encontrado) else (echo ✗ index.html NO encontrado & set "archivos_faltantes=1")
if not exist "clustering_visualizer.py" (echo ✓ clustering_visualizer.py encontrado) else (echo ✗ clustering_visualizer.py NO encontrado & set "archivos_faltantes=1")
if not exist "app.py" (echo ✓ app.py encontrado) else (echo ✗ app.py NO encontrado & set "archivos_faltantes=1")
if not exist "PaginaGraficos\graficostablas.py" (echo ✓ graficostablas.py encontrado) else (echo ✗ graficostablas.py NO encontrado & set "archivos_faltantes=1")

if defined archivos_faltantes (
    echo.
    echo ✗ Faltan archivos del proyecto
    echo   Asegurate de que todos los archivos esten en su lugar
    cd Instalador
    pause
    exit /b 1
)

echo.
echo ========================================
echo  VERIFICACION COMPLETADA
echo ========================================
echo.
echo ✓ Python instalado y funcionando
echo ✓ Todas las dependencias instaladas
echo ✓ Todos los archivos del proyecto presentes
echo.
echo El proyecto esta listo para ejecutarse.
echo Usa: ejecutar_proyecto.bat
echo.
cd Instalador
pause
