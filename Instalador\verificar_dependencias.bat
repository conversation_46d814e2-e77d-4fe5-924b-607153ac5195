@echo off
echo ========================================
echo  VERIFICADOR DE DEPENDENCIAS - AUSTIMO
echo ========================================
echo.

REM Cambiar al directorio padre del proyecto
cd ..

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python NO instalado
    echo   Ejecuta: Instalador\instalar_proyecto.bat
    goto :error
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✓ Python %%i instalado
)

echo.
echo Verificando librerías Python...

REM Verificar cada dependencia
python -c "import pandas; print('✓ pandas ' + pandas.__version__)" 2>nul || (echo ✗ pandas NO instalado & set error=1)
python -c "import numpy; print('✓ numpy ' + numpy.__version__)" 2>nul || (echo ✗ numpy NO instalado & set error=1)
python -c "import matplotlib; print('✓ matplotlib ' + matplotlib.__version__)" 2>nul || (echo ✗ matplotlib NO instalado & set error=1)
python -c "import seaborn; print('✓ seaborn ' + seaborn.__version__)" 2>nul || (echo ✗ seaborn NO instalado & set error=1)
python -c "import sklearn; print('✓ scikit-learn ' + sklearn.__version__)" 2>nul || (echo ✗ scikit-learn NO instalado & set error=1)
python -c "import scipy; print('✓ scipy ' + scipy.__version__)" 2>nul || (echo ✗ scipy NO instalado & set error=1)
python -c "import flask; print('✓ flask ' + flask.__version__)" 2>nul || (echo ✗ flask NO instalado & set error=1)
python -c "import plotly; print('✓ plotly ' + plotly.__version__)" 2>nul || (echo ✗ plotly NO instalado & set error=1)

echo.
echo Verificando archivos del proyecto...

REM Verificar archivos principales
if exist "datos.csv" (echo ✓ datos.csv encontrado) else (echo ✗ datos.csv NO encontrado & set error=1)
if exist "index.html" (echo ✓ index.html encontrado) else (echo ✗ index.html NO encontrado & set error=1)
if exist "clustering_visualizer.py" (echo ✓ clustering_visualizer.py encontrado) else (echo ✗ clustering_visualizer.py NO encontrado & set error=1)
if exist "app.py" (echo ✓ app.py encontrado) else (echo ✗ app.py NO encontrado & set error=1)
if exist "PaginaGraficos\graficostablas.py" (echo ✓ graficostablas.py encontrado) else (echo ✗ graficostablas.py NO encontrado & set error=1)
if exist "PaginaGraficos\generar_graficos.bat" (echo ✓ generar_graficos.bat encontrado) else (echo ✗ generar_graficos.bat NO encontrado & set error=1)

echo.
if defined error (
    echo ========================================
    echo  ERRORES DETECTADOS
    echo ========================================
    echo.
    echo Algunas dependencias o archivos faltan.
    echo Ejecuta: Instalador\instalar_proyecto.bat
    echo.
    goto :end
) else (
    echo ========================================
    echo  VERIFICACION EXITOSA
    echo ========================================
    echo.
    echo ✓ Todas las dependencias están instaladas
    echo ✓ Todos los archivos están presentes
    echo.
    echo El proyecto está listo para ejecutarse.
    echo Ejecuta: Instalador\ejecutar_proyecto.bat
    echo.
)

:end
cd Instalador
pause
exit /b 0

:error
echo.
cd Instalador
pause
exit /b 1
